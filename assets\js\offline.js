/**
 * Offline Mode Handler for Restaurant Management System
 * 
 * This script handles:
 * 1. Detecting online/offline status
 * 2. Storing data locally when offline
 * 3. Syncing data when back online
 * 4. Providing offline UI indicators
 */

class OfflineManager {
    constructor() {
        // Initialize properties
        this.isOnline = navigator.onLine;
        this.pendingOperations = [];
        this.offlineIndicator = null;
        
        // Load pending operations from localStorage
        this.loadPendingOperations();
        
        // Initialize the offline indicator
        this.createOfflineIndicator();
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Check initial state
        this.updateOnlineStatus();
        
        // Log initialization
        console.log('Offline Manager initialized, online status:', this.isOnline);
    }
    
    // Create the offline indicator element
    createOfflineIndicator() {
        this.offlineIndicator = document.createElement('div');
        this.offlineIndicator.className = 'offline-indicator';
        this.offlineIndicator.innerHTML = '<i class="material-icons">cloud_off</i> <PERSON><PERSON> làm việc ngo<PERSON> tuyến';
        document.body.appendChild(this.offlineIndicator);
    }
    
    // Set up event listeners for online/offline events
    setupEventListeners() {
        window.addEventListener('online', this.handleOnline.bind(this));
        window.addEventListener('offline', this.handleOffline.bind(this));
        
        // Add submit event listener to forms to intercept when offline
        document.addEventListener('submit', this.handleFormSubmit.bind(this));
        
        // Add click event listener to intercept fetch actions
        document.addEventListener('click', this.handleButtonClick.bind(this));
    }
    
    // Handler for online event
    handleOnline() {
        this.isOnline = true;
        this.updateOnlineStatus();
        
        // Show reconnection message
        this.showToast('Kết nối mạng đã được khôi phục', 'success');
        
        // Attempt to sync pending operations
        this.syncPendingOperations();
    }
    
    // Handler for offline event
    handleOffline() {
        this.isOnline = false;
        this.updateOnlineStatus();
        
        // Show disconnection message
        this.showToast('Bạn đang làm việc ngoại tuyến', 'warning');
    }
    
    // Update UI to reflect online status
    updateOnlineStatus() {
        if (this.isOnline) {
            this.offlineIndicator.classList.remove('visible');
            document.body.classList.remove('offline-mode');
        } else {
            this.offlineIndicator.classList.add('visible');
            document.body.classList.add('offline-mode');
        }
    }
    
    // Intercept form submissions when offline
    handleFormSubmit(event) {
        if (!this.isOnline && !event.target.hasAttribute('data-allow-offline')) {
            event.preventDefault();
            
            // Store the form data for later submission
            const formData = new FormData(event.target);
            const formDataObj = {};
            
            formData.forEach((value, key) => {
                formDataObj[key] = value;
            });
            
            this.addPendingOperation({
                type: 'form',
                url: event.target.action,
                method: event.target.method,
                data: formDataObj,
                timestamp: new Date().toISOString()
            });
            
            // Show message to user
            this.showToast('Dữ liệu đã được lưu và sẽ được gửi khi có kết nối', 'info');
            
            // If the form has a redirect, store it to simulate form submission
            const redirectInput = event.target.querySelector('input[name="redirect"]');
            if (redirectInput) {
                this.simulateRedirect(redirectInput.value);
            }
        }
    }
    
    // Intercept button clicks for AJAX operations when offline
    handleButtonClick(event) {
        if (!this.isOnline) {
            const button = event.target.closest('[data-fetch-url]');
            if (button && !button.hasAttribute('data-allow-offline')) {
                event.preventDefault();
                
                // Store the operation for later
                this.addPendingOperation({
                    type: 'fetch',
                    url: button.dataset.fetchUrl,
                    method: button.dataset.fetchMethod || 'GET',
                    data: button.dataset.fetchData ? JSON.parse(button.dataset.fetchData) : {},
                    timestamp: new Date().toISOString()
                });
                
                // Show message to user
                this.showToast('Yêu cầu đã được lưu và sẽ được thực hiện khi có kết nối', 'info');
            }
        }
    }
    
    // Add a new pending operation and save to localStorage
    addPendingOperation(operation) {
        this.pendingOperations.push(operation);
        this.savePendingOperations();
        
        // Update the UI to show pending operations count
        this.updatePendingOperationsUI();
    }
    
    // Save pending operations to localStorage
    savePendingOperations() {
        localStorage.setItem('pendingOperations', JSON.stringify(this.pendingOperations));
    }
    
    // Load pending operations from localStorage
    loadPendingOperations() {
        const saved = localStorage.getItem('pendingOperations');
        this.pendingOperations = saved ? JSON.parse(saved) : [];
        
        // Update the UI to show pending operations count
        this.updatePendingOperationsUI();
    }
    
    // Update UI to show pending operations count
    updatePendingOperationsUI() {
        const count = this.pendingOperations.length;
        
        // Update the indicator text
        if (count > 0) {
            this.offlineIndicator.innerHTML = `<i class="material-icons">cloud_off</i> ${count} thao tác đang chờ`;
        } else {
            this.offlineIndicator.innerHTML = '<i class="material-icons">cloud_off</i> Đang làm việc ngoại tuyến';
        }
        
        // If a pending operations badge exists, update it
        const badge = document.querySelector('.pending-operations-badge');
        if (badge) {
            if (count > 0) {
                badge.textContent = count;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        }
    }
    
    // Sync pending operations when back online
    async syncPendingOperations() {
        if (!this.isOnline || this.pendingOperations.length === 0) {
            return;
        }
        
        console.log('Syncing pending operations:', this.pendingOperations.length);
        
        // Show syncing message
        this.showToast(`Đang đồng bộ ${this.pendingOperations.length} thao tác...`, 'info');
        
        // Process each operation in order
        const operations = [...this.pendingOperations];
        this.pendingOperations = [];
        
        let successCount = 0;
        let failCount = 0;
        
        for (const operation of operations) {
            try {
                if (operation.type === 'form') {
                    await this.syncFormOperation(operation);
                } else if (operation.type === 'fetch') {
                    await this.syncFetchOperation(operation);
                }
                successCount++;
            } catch (error) {
                console.error('Error syncing operation:', error, operation);
                // Put the operation back in the queue
                this.pendingOperations.push(operation);
                failCount++;
            }
        }
        
        // Save the updated pending operations
        this.savePendingOperations();
        this.updatePendingOperationsUI();
        
        // Show completion message
        if (successCount > 0) {
            this.showToast(`Đã đồng bộ thành công ${successCount} thao tác`, 'success');
        }
        
        if (failCount > 0) {
            this.showToast(`Không thể đồng bộ ${failCount} thao tác, sẽ thử lại sau`, 'warning');
        }
    }
    
    // Sync a form submission operation
    async syncFormOperation(operation) {
        const formData = new FormData();
        
        // Add all form fields to the FormData object
        for (const [key, value] of Object.entries(operation.data)) {
            formData.append(key, value);
        }
        
        // Add an indicator that this is a sync from offline mode
        formData.append('offline_sync', 'true');
        
        // Submit the form data
        const response = await fetch(operation.url, {
            method: operation.method,
            body: formData,
            headers: {
                'X-Offline-Sync': 'true'
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return response;
    }
    
    // Sync a fetch operation
    async syncFetchOperation(operation) {
        const options = {
            method: operation.method,
            headers: {
                'Content-Type': 'application/json',
                'X-Offline-Sync': 'true'
            }
        };
        
        if (operation.method !== 'GET' && operation.data) {
            options.body = JSON.stringify(operation.data);
        }
        
        const response = await fetch(operation.url, options);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return response;
    }
    
    // Simulate a redirect after offline form submission
    simulateRedirect(url) {
        // Store current page in history for back button
        history.pushState({ offlineRedirect: true }, '', window.location.href);
        
        // Redirect to the specified URL
        window.location.href = url;
    }
    
    // Show a toast notification
    showToast(message, type = 'info') {
        // Use the site's toast notification system if available
        if (typeof window.showToast === 'function') {
            window.showToast(message, type);
            return;
        }
        
        // Simple fallback for toast notifications
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        // Animate in
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);
        
        // Remove after 3 seconds
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }
    
    // Check if there are pending operations
    hasPendingOperations() {
        return this.pendingOperations.length > 0;
    }
    
    // Get the number of pending operations
    getPendingOperationsCount() {
        return this.pendingOperations.length;
    }
}

// Initialize the offline manager when the DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.offlineManager = new OfflineManager();
    
    // Add style for toast fallback if not already defined
    if (!document.getElementById('offline-toast-styles')) {
        const style = document.createElement('style');
        style.id = 'offline-toast-styles';
        style.textContent = `
            .toast {
                position: fixed;
                bottom: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 4px;
                color: white;
                max-width: 300px;
                box-shadow: 0 3px 6px rgba(0,0,0,0.16);
                opacity: 0;
                transform: translateY(20px);
                transition: all 0.3s ease-in-out;
                z-index: 9999;
            }
            
            .toast.show {
                opacity: 1;
                transform: translateY(0);
            }
            
            .toast-info {
                background-color: #3b71ca;
            }
            
            .toast-success {
                background-color: #14a44d;
            }
            
            .toast-warning {
                background-color: #e4a11b;
            }
            
            .toast-danger {
                background-color: #dc4c64;
            }
            
            .pending-operations-badge {
                position: absolute;
                top: -8px;
                right: -8px;
                background-color: #dc4c64;
                color: white;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: bold;
            }
        `;
        document.head.appendChild(style);
    }
    
    // Add a badge to the sync button if it exists
    const syncButton = document.querySelector('.sync-button');
    if (syncButton) {
        const badge = document.createElement('span');
        badge.className = 'pending-operations-badge';
        badge.style.display = 'none';
        syncButton.style.position = 'relative';
        syncButton.appendChild(badge);
    }
});