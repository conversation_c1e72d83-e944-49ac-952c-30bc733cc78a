<?php
require_once __DIR__ . '/config.php';

// Redirect if not logged in (except for login and password reset pages)
$current_page = basename($_SERVER['PHP_SELF']);
$allowed_pages = ['login.php', 'reset_password.php', 'forgot_password.php'];

if (!isLoggedIn() && !in_array($current_page, $allowed_pages)) {
    redirect(SITE_URL . '/auth/login.php');
}
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H<PERSON></title>
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- MDB -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.0/mdb.min.css" rel="stylesheet" />
    <!-- Custom CSS -->
    <link href="<?= ASSETS_URL ?>/css/style.css" rel="stylesheet" />
    <link href="<?= ASSETS_URL ?>/css/custom.css" rel="stylesheet" />
    <!-- Mobile-optimized styles -->
    <link href="<?= ASSETS_URL ?>/css/mobile.css" rel="stylesheet" />
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay d-none">
        <div class="spinner-border text-primary loading-spinner" role="status">
            <span class="visually-hidden">Đang tải...</span>
        </div>
    </div>

    <!-- Toast Container for Notifications -->
    <div class="toast-container"></div>

    <?php if(isLoggedIn() && !in_array($current_page, $allowed_pages)): ?>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container-fluid px-4">
            <a class="navbar-brand d-flex align-items-center" href="<?= SITE_URL ?>">
                <div class="brand-icon me-2 text-primary">
                    <i class="fas fa-utensils fa-lg"></i>
                </div>
                <span class="brand-text fw-bold">RestaurantPro</span>
            </a>
            <button class="navbar-toggler shadow-0" type="button" data-mdb-toggle="collapse" data-mdb-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <i class="fas fa-bars"></i>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <?php if(isAdmin()): ?>
                    <!-- Admin Menu -->
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center <?= $current_page == 'index.php' ? 'active' : '' ?>" href="<?= ADMIN_URL ?>">
                            <i class="material-icons me-2 fs-5">dashboard</i>Trang chủ
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="foodManagementDropdown" role="button"
                            data-mdb-toggle="dropdown" aria-expanded="false">
                            <i class="material-icons me-2 fs-5">restaurant_menu</i>Quản lý món ăn
                        </a>
                        <ul class="dropdown-menu shadow-sm" aria-labelledby="foodManagementDropdown">
                            <li>
                                <a class="dropdown-item d-flex align-items-center" href="<?= ADMIN_URL ?>/food_items.php">
                                    <i class="material-icons me-2 text-primary">menu_book</i>Danh sách món ăn
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center" href="<?= ADMIN_URL ?>/add_food.php">
                                    <i class="material-icons me-2 text-success">add_circle</i>Thêm món ăn mới
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center" href="<?= ADMIN_URL ?>/categories.php">
                                    <i class="material-icons me-2 text-info">category</i>Quản lý danh mục
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center <?= $current_page == 'tables.php' ? 'active' : '' ?>" href="<?= ADMIN_URL ?>/tables.php">
                            <i class="material-icons me-2 fs-5">table_restaurant</i>Quản lý bàn
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center <?= $current_page == 'users.php' ? 'active' : '' ?>" href="<?= ADMIN_URL ?>/users.php">
                            <i class="material-icons me-2 fs-5">people</i>Tài khoản
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center <?= $current_page == 'reports.php' ? 'active' : '' ?>" href="<?= ADMIN_URL ?>/reports.php">
                            <i class="material-icons me-2 fs-5">analytics</i>Báo cáo
                        </a>
                    </li>
                    <?php elseif(isStaff()): ?>
                    <!-- Staff Menu -->
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center <?= $current_page == 'index.php' ? 'active' : '' ?>" href="<?= STAFF_URL ?>">
                            <i class="material-icons me-2 fs-5">dashboard</i>Trang chủ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center <?= $current_page == 'tables.php' ? 'active' : '' ?>" href="<?= STAFF_URL ?>/tables.php">
                            <i class="material-icons me-2 fs-5">table_restaurant</i>Quản lý bàn
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center <?= $current_page == 'orders.php' ? 'active' : '' ?>" href="<?= STAFF_URL ?>/orders.php">
                            <i class="material-icons me-2 fs-5">receipt_long</i>Hóa đơn
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center <?= $current_page == 'daily_revenue.php' ? 'active' : '' ?>" href="<?= STAFF_URL ?>/daily_revenue.php">
                            <i class="material-icons me-2 fs-5">attach_money</i>Doanh thu ngày
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <!-- Common Menu Items -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button"
                            data-mdb-toggle="dropdown" aria-expanded="false">
                            <div class="user-avatar me-2 rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                <i class="material-icons text-primary">person</i>
                            </div>
                            <span class="user-name d-none d-md-inline"><?= $_SESSION['full_name'] ?? $_SESSION['username'] ?? 'Tài khoản' ?></span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow-sm" aria-labelledby="navbarDropdown">
                            <li class="dropdown-header">
                                <div class="d-flex align-items-center">
                                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                        <i class="material-icons text-primary">person</i>
                                    </div>
                                    <div>
                                        <div class="fw-bold"><?= $_SESSION['full_name'] ?? $_SESSION['username'] ?></div>
                                        <small class="text-muted"><?= isAdmin() ? 'Quản trị viên' : 'Nhân viên' ?></small>
                                    </div>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider" /></li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center" href="<?= SITE_URL ?>/profile.php">
                                    <i class="material-icons me-2 text-info">account_circle</i>Thông tin cá nhân
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center" href="<?= SITE_URL ?>/change_password.php">
                                    <i class="material-icons me-2 text-warning">vpn_key</i>Đổi mật khẩu
                                </a>
                            </li>
                            <li><hr class="dropdown-divider" /></li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center text-danger" href="<?= SITE_URL ?>/auth/logout.php">
                                    <i class="material-icons me-2">logout</i>Đăng xuất
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <?php endif; ?>

    <!-- Main Content Container -->
    <div class="container-fluid px-4 my-4">
        <div id="main-content" class="fade-in">
            <?php displayFlashMessage(); ?>