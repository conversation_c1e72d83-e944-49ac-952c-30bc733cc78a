<?php
require_once '../includes/header.php';

// Check if user is admin
if (!isAdmin()) {
    setFlashMessage('danger', 'Bạn không có quyền truy cập trang này');
    redirect(SITE_URL);
}

// Handle delete request
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $user_id = (int)$_GET['delete'];
    
    // Prevent deleting your own account
    if ($user_id == $_SESSION['user_id']) {
        setFlashMessage('danger', 'Bạn không thể xóa tài khoản của chính mình');
    } else {
        // Check if the user has any orders
        $check_sql = "SELECT COUNT(*) as count FROM orders WHERE user_id = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("i", $user_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        $order_count = $check_result->fetch_assoc()['count'];
        
        if ($order_count > 0) {
            setFlashMessage('danger', 'Không thể xóa người dùng này vì có ' . $order_count . ' đơn hàng liên quan');
        } else {
            // Delete the user
            $delete_sql = "DELETE FROM users WHERE user_id = ?";
            $delete_stmt = $conn->prepare($delete_sql);
            $delete_stmt->bind_param("i", $user_id);
            
            if ($delete_stmt->execute()) {
                setFlashMessage('success', 'Người dùng đã được xóa thành công');
            } else {
                setFlashMessage('danger', 'Không thể xóa người dùng: ' . $conn->error);
            }
        }
    }
    
    redirect(ADMIN_URL . '/users.php');
}

// Get all users
$sql = "SELECT u.*, r.role_name FROM users u 
        JOIN roles r ON u.role_id = r.role_id 
        ORDER BY u.username";
$result = $conn->query($sql);
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h2>Quản lý người dùng</h2>
    </div>
    <div class="col-md-6 text-end">
        <a href="add_user.php" class="btn btn-primary">
            <i class="fas fa-user-plus me-2"></i>Thêm người dùng mới
        </a>
    </div>
</div>

<!-- Users List -->
<div class="card">
    <div class="card-body">
        <?php if ($result->num_rows > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Tên đăng nhập</th>
                            <th>Họ và tên</th>
                            <th>Email</th>
                            <th>Vai trò</th>
                            <th>Trạng thái</th>
                            <th>Ngày tạo</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($user = $result->fetch_assoc()): ?>
                            <tr>
                                <td><?= $user['user_id'] ?></td>
                                <td><?= htmlspecialchars($user['username']) ?></td>
                                <td><?= htmlspecialchars($user['full_name']) ?></td>
                                <td><?= htmlspecialchars($user['email']) ?></td>
                                <td><?= htmlspecialchars($user['role_name']) ?></td>
                                <td>
                                    <?php if ($user['status'] == 1): ?>
                                        <span class="badge bg-success">Hoạt động</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Vô hiệu</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= date('d/m/Y', strtotime($user['created_at'])) ?></td>
                                <td>
                                    <a href="edit_user.php?id=<?= $user['user_id'] ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php if ($user['user_id'] != $_SESSION['user_id']): ?>
                                        <a href="users.php?delete=<?= $user['user_id'] ?>" class="btn btn-sm btn-danger btn-delete" onclick="return confirm('Bạn có chắc chắn muốn xóa người dùng này?');">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <p class="text-center">Chưa có người dùng nào trong hệ thống</p>
        <?php endif; ?>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>