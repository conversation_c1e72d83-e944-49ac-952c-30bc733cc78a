<?php
require_once '../includes/header.php';

// Check if user is staff
if (!isStaff()) {
    setFlashMessage('danger', 'Bạn không có quyền truy cập trang này');
    redirect(SITE_URL);
}

// Get all tables
$sql = "SELECT * FROM tables ORDER BY table_number";
$result = $conn->query($sql);
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">
        <i class="material-icons text-primary me-2">table_restaurant</i>
        Quản lý bàn
    </h2>
    <div class="d-flex gap-2">
        <!-- Search box -->
        <div class="input-group w-auto">
            <input type="text" class="form-control" placeholder="Tìm bàn..." id="tableSearch" aria-label="Tìm kiếm bàn">
            <button class="btn btn-primary search-button" type="button">
                <i class="material-icons">search</i>
            </button>
        </div>
        
        <!-- Status filter dropdown -->
        <div class="dropdown">
            <button class="btn btn-outline-primary dropdown-toggle" type="button" id="statusFilterDropdown" data-mdb-toggle="dropdown" aria-expanded="false">
                <i class="material-icons me-1">filter_list</i>
                Lọc
            </button>
            <ul class="dropdown-menu shadow-sm" aria-labelledby="statusFilterDropdown">
                <li><a class="dropdown-item active" href="#" onclick="filterTablesByStatus('all')">Tất cả</a></li>
                <li><a class="dropdown-item" href="#" onclick="filterTablesByStatus('available')">Trống</a></li>
                <li><a class="dropdown-item" href="#" onclick="filterTablesByStatus('occupied')">Đang sử dụng</a></li>
                <li><a class="dropdown-item" href="#" onclick="filterTablesByStatus('reserved')">Đã đặt trước</a></li>
                <li><a class="dropdown-item" href="#" onclick="filterTablesByStatus('maintenance')">Bảo trì</a></li>
            </ul>
        </div>
    </div>
</div>

<!-- Table Status Overview -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card dashboard-card bg-white text-success">
            <div class="card-body">
                <div class="icon-bg bg-success bg-opacity-10">
                    <i class="material-icons">check_circle</i>
                </div>
                <h5 class="card-title">Bàn trống</h5>
                <?php
                $available_count = 0;
                $result->data_seek(0);
                while ($table = $result->fetch_assoc()) {
                    if ($table['status'] == 'available') {
                        $available_count++;
                    }
                }
                ?>
                <h3 class="text-success"><?= $available_count ?></h3>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card dashboard-card bg-white text-danger">
            <div class="card-body">
                <div class="icon-bg bg-danger bg-opacity-10">
                    <i class="material-icons">people</i>
                </div>
                <h5 class="card-title">Đang sử dụng</h5>
                <?php
                $occupied_count = 0;
                $result->data_seek(0);
                while ($table = $result->fetch_assoc()) {
                    if ($table['status'] == 'occupied') {
                        $occupied_count++;
                    }
                }
                ?>
                <h3 class="text-danger"><?= $occupied_count ?></h3>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card dashboard-card bg-white text-warning">
            <div class="card-body">
                <div class="icon-bg bg-warning bg-opacity-10">
                    <i class="material-icons">calendar_month</i>
                </div>
                <h5 class="card-title">Đã đặt trước</h5>
                <?php
                $reserved_count = 0;
                $result->data_seek(0);
                while ($table = $result->fetch_assoc()) {
                    if ($table['status'] == 'reserved') {
                        $reserved_count++;
                    }
                }
                ?>
                <h3 class="text-warning"><?= $reserved_count ?></h3>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card dashboard-card bg-white text-info">
            <div class="card-body">
                <div class="icon-bg bg-info bg-opacity-10">
                    <i class="material-icons">build</i>
                </div>
                <h5 class="card-title">Bảo trì</h5>
                <?php
                $maintenance_count = 0;
                $result->data_seek(0);
                while ($table = $result->fetch_assoc()) {
                    if ($table['status'] == 'maintenance') {
                        $maintenance_count++;
                    }
                }
                ?>
                <h3 class="text-info"><?= $maintenance_count ?></h3>
            </div>
        </div>
    </div>
</div>

<!-- Tables Grid -->
<div class="row table-management auto-refresh">
    <?php if ($result->num_rows > 0): ?>
        <?php 
        $result->data_seek(0);
        while ($table = $result->fetch_assoc()): 
            // Determine status classes and text
            $status_text = '';
            $status_class = '';
            $icon = '';
            
            switch ($table['status']) {
                case 'available':
                    $status_text = 'Trống';
                    $status_class = 'success';
                    $icon = 'check_circle';
                    break;
                case 'occupied':
                    $status_text = 'Đang sử dụng';
                    $status_class = 'danger';
                    $icon = 'people';
                    break;
                case 'reserved':
                    $status_text = 'Đã đặt trước';
                    $status_class = 'warning';
                    $icon = 'calendar_month';
                    break;
                case 'maintenance':
                    $status_text = 'Bảo trì';
                    $status_class = 'info';
                    $icon = 'build';
                    break;
            }
            
            // Get order for occupied tables
            $order_id = null;
            if ($table['status'] == 'occupied') {
                $order_sql = "SELECT o.order_id, o.order_date 
                            FROM orders o
                            WHERE o.table_id = ? AND o.status IN ('pending', 'processing') 
                            ORDER BY o.order_date DESC LIMIT 1";
                $order_stmt = $conn->prepare($order_sql);
                $order_stmt->bind_param("i", $table['table_id']);
                $order_stmt->execute();
                $order_result = $order_stmt->get_result();
                
                if ($order_result->num_rows > 0) {
                    $order = $order_result->fetch_assoc();
                    $order_id = $order['order_id'];
                }
            }
        ?>
            <div class="col-xl-3 col-lg-4 col-md-6 mb-4 table-item" data-status="<?= $table['status'] ?>" data-table-number="<?= htmlspecialchars($table['table_number']) ?>">
                <div class="card table-card table-<?= $table['status'] ?>" data-table-id="<?= $table['table_id'] ?>">
                    <div class="card-header bg-<?= $status_class ?> bg-opacity-10 d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-<?= $status_class ?>">
                            <i class="material-icons me-1"><?= $icon ?></i>
                            Bàn <?= htmlspecialchars($table['table_number']) ?>
                        </h5>
                        <span class="badge bg-<?= $status_class ?> rounded-pill status-badge"><?= $status_text ?></span>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <p class="card-text mb-1"><i class="material-icons fs-5 me-1 text-muted">people</i> <?= $table['capacity'] ?> người</p>
                                <?php if ($order_id): ?>
                                    <p class="card-text text-muted mb-0">
                                        <i class="material-icons fs-5 me-1">receipt_long</i> 
                                        Đơn #<?= $order_id ?>
                                    </p>
                                <?php endif; ?>
                            </div>
                            <div class="table-actions">
                                <div class="dropdown">
                                    <button class="btn btn-light btn-floating shadow-0" type="button" id="tableActionDropdown<?= $table['table_id'] ?>" data-mdb-toggle="dropdown" aria-expanded="false">
                                        <i class="material-icons">more_vert</i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end shadow-sm" aria-labelledby="tableActionDropdown<?= $table['table_id'] ?>">
                                        <?php if ($table['status'] == 'available' || $table['status'] == 'reserved'): ?>
                                            <li>
                                                <a class="dropdown-item d-flex align-items-center" href="new_order.php?table_id=<?= $table['table_id'] ?>">
                                                    <i class="material-icons me-2 text-success">add_circle</i>Tạo đơn mới
                                                </a>
                                            </li>
                                        <?php elseif ($table['status'] == 'occupied' && $order_id): ?>
                                            <li>
                                                <a class="dropdown-item d-flex align-items-center" href="edit_order.php?id=<?= $order_id ?>">
                                                    <i class="material-icons me-2 text-primary">edit</i>Sửa đơn hàng
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item d-flex align-items-center" href="payment.php?id=<?= $order_id ?>">
                                                    <i class="material-icons me-2 text-success">payments</i>Thanh toán
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item d-flex align-items-center" href="transfer_table.php?order_id=<?= $order_id ?>">
                                                    <i class="material-icons me-2 text-info">swap_horiz</i>Chuyển bàn
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                        <li>
                                            <a class="dropdown-item d-flex align-items-center" href="view_table.php?id=<?= $table['table_id'] ?>">
                                                <i class="material-icons me-2 text-info">info</i>Xem chi tiết
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <?php if ($table['status'] == 'available'): ?>
                            <a href="new_order.php?table_id=<?= $table['table_id'] ?>" class="btn btn-success btn-sm w-100 d-flex align-items-center justify-content-center">
                                <i class="material-icons me-2">add_circle</i>Tạo đơn mới
                            </a>
                        <?php elseif ($table['status'] == 'occupied'): ?>
                            <?php if ($order_id): ?>
                                <div class="d-grid gap-2">
                                    <div class="btn-group">
                                        <a href="edit_order.php?id=<?= $order_id ?>" class="btn btn-primary btn-sm">
                                            <i class="material-icons me-1">edit</i>Sửa đơn
                                        </a>
                                        <a href="payment.php?id=<?= $order_id ?>" class="btn btn-success btn-sm">
                                            <i class="material-icons me-1">payments</i>Thanh toán
                                        </a>
                                    </div>
                                    <a href="transfer_table.php?order_id=<?= $order_id ?>" class="btn btn-outline-secondary btn-sm">
                                        <i class="material-icons me-1">swap_horiz</i>Chuyển bàn
                                    </a>
                                </div>
                            <?php else: ?>
                                <a href="new_order.php?table_id=<?= $table['table_id'] ?>" class="btn btn-primary btn-sm w-100">
                                    <i class="material-icons me-2">add_circle</i>Tạo đơn mới
                                </a>
                            <?php endif; ?>
                        <?php elseif ($table['status'] == 'reserved'): ?>
                            <a href="new_order.php?table_id=<?= $table['table_id'] ?>" class="btn btn-warning btn-sm w-100">
                                <i class="material-icons me-2">add_circle</i>Tạo đơn mới
                            </a>
                        <?php else: ?>
                            <button class="btn btn-secondary btn-sm w-100" disabled>
                                <i class="material-icons me-2">build</i>Đang bảo trì
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endwhile; ?>
    <?php else: ?>
        <div class="col-12">
            <div class="alert alert-info d-flex align-items-center" role="alert">
                <i class="material-icons me-2">info</i>
                <div>Không có bàn nào trong hệ thống</div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Table Status Legend -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0">Chú thích trạng thái</h5>
    </div>
    <div class="card-body">
        <div class="d-flex flex-wrap gap-4">
            <div class="d-flex align-items-center">
                <span class="status-dot status-available me-2"></span> 
                <span>Trống</span>
                <span class="text-muted ms-1">(<?= $available_count ?>)</span>
            </div>
            <div class="d-flex align-items-center">
                <span class="status-dot status-occupied me-2"></span> 
                <span>Đang sử dụng</span>
                <span class="text-muted ms-1">(<?= $occupied_count ?>)</span>
            </div>
            <div class="d-flex align-items-center">
                <span class="status-dot status-reserved me-2"></span> 
                <span>Đã đặt trước</span>
                <span class="text-muted ms-1">(<?= $reserved_count ?>)</span>
            </div>
            <div class="d-flex align-items-center">
                <span class="status-dot status-maintenance me-2"></span> 
                <span>Bảo trì</span>
                <span class="text-muted ms-1">(<?= $maintenance_count ?>)</span>
            </div>
        </div>
    </div>
</div>

<script>
// Table search and filtering functions
function filterTablesByStatus(status) {
    const tableItems = document.querySelectorAll('.table-item');
    
    tableItems.forEach(item => {
        if (status === 'all' || item.dataset.status === status) {
            item.style.display = '';
        } else {
            item.style.display = 'none';
        }
    });
    
    // Update active state on filter buttons
    document.querySelectorAll('#statusFilterDropdown + .dropdown-menu .dropdown-item').forEach(link => {
        link.classList.remove('active');
    });
    
    document.querySelector(`#statusFilterDropdown + .dropdown-menu .dropdown-item[onclick*="${status}"]`).classList.add('active');
}

document.addEventListener('DOMContentLoaded', function() {
    // Initialize table search
    const searchInput = document.getElementById('tableSearch');
    searchInput.addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();
        const tableItems = document.querySelectorAll('.table-item');
        
        tableItems.forEach(item => {
            const tableNumber = item.dataset.tableNumber.toLowerCase();
            if (tableNumber.includes(searchTerm)) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    });
});
</script>

<?php require_once '../includes/footer.php'; ?>