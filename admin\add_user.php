<?php
require_once '../includes/header.php';

// Check if user is admin
if (!isAdmin()) {
    setFlashMessage('danger', 'Bạn không có quyền truy cập trang này');
    redirect(SITE_URL);
}

// Get all roles
$role_sql = "SELECT role_id, role_name FROM roles ORDER BY role_id";
$role_result = $conn->query($role_sql);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = sanitize($_POST['username']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $email = sanitize($_POST['email']);
    $full_name = sanitize($_POST['full_name']);
    $role_id = (int)$_POST['role_id'];
    $status = isset($_POST['status']) ? 1 : 0;
    $error = '';
    
    // Validate inputs
    if (empty($username)) {
        $error = 'Vui lòng nhập tên đăng nhập';
    } elseif (strlen($username) < 3) {
        $error = 'Tên đăng nhập phải có ít nhất 3 ký tự';
    } elseif (empty($password)) {
        $error = 'Vui lòng nhập mật khẩu';
    } elseif (strlen($password) < 6) {
        $error = 'Mật khẩu phải có ít nhất 6 ký tự';
    } elseif ($password != $confirm_password) {
        $error = 'Mật khẩu xác nhận không khớp';
    } elseif (empty($email)) {
        $error = 'Vui lòng nhập email';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Email không hợp lệ';
    } elseif (empty($full_name)) {
        $error = 'Vui lòng nhập họ và tên';
    } elseif ($role_id <= 0) {
        $error = 'Vui lòng chọn vai trò';
    } else {
        // Check if username already exists
        $check_username_sql = "SELECT user_id FROM users WHERE username = ?";
        $check_username_stmt = $conn->prepare($check_username_sql);
        $check_username_stmt->bind_param("s", $username);
        $check_username_stmt->execute();
        $check_username_result = $check_username_stmt->get_result();
        
        if ($check_username_result->num_rows > 0) {
            $error = 'Tên đăng nhập đã tồn tại';
        } else {
            // Check if email already exists
            $check_email_sql = "SELECT user_id FROM users WHERE email = ?";
            $check_email_stmt = $conn->prepare($check_email_sql);
            $check_email_stmt->bind_param("s", $email);
            $check_email_stmt->execute();
            $check_email_result = $check_email_stmt->get_result();
            
            if ($check_email_result->num_rows > 0) {
                $error = 'Email đã tồn tại';
            } else {
                // Hash the password
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                // Insert new user
                $sql = "INSERT INTO users (username, password, email, full_name, role_id, status) 
                        VALUES (?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("ssssis", $username, $hashed_password, $email, $full_name, $role_id, $status);
                
                if ($stmt->execute()) {
                    setFlashMessage('success', 'Người dùng mới đã được thêm thành công');
                    redirect(ADMIN_URL . '/users.php');
                } else {
                    $error = 'Đã xảy ra lỗi khi thêm người dùng: ' . $conn->error;
                }
            }
        }
    }
}
?>

<div class="row mb-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= ADMIN_URL ?>">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="<?= ADMIN_URL ?>/users.php">Người dùng</a></li>
                <li class="breadcrumb-item active" aria-current="page">Thêm người dùng mới</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Thêm người dùng mới</h5>
            </div>
            <div class="card-body">
                <?php if (isset($error) && !empty($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?= $error ?>
                        <button type="button" class="btn-close" data-mdb-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <form method="post" action="">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-outline">
                                <input type="text" id="username" name="username" class="form-control" required value="<?= htmlspecialchars($_POST['username'] ?? '') ?>" />
                                <label class="form-label" for="username">Tên đăng nhập</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-outline">
                                <input type="text" id="full_name" name="full_name" class="form-control" required value="<?= htmlspecialchars($_POST['full_name'] ?? '') ?>" />
                                <label class="form-label" for="full_name">Họ và tên</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-outline">
                            <input type="email" id="email" name="email" class="form-control" required value="<?= htmlspecialchars($_POST['email'] ?? '') ?>" />
                            <label class="form-label" for="email">Email</label>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-outline">
                                <input type="password" id="password" name="password" class="form-control" required />
                                <label class="form-label" for="password">Mật khẩu</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-outline">
                                <input type="password" id="confirm_password" name="confirm_password" class="form-control" required />
                                <label class="form-label" for="confirm_password">Xác nhận mật khẩu</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <select class="form-select" name="role_id" required>
                                <option value="">Chọn vai trò</option>
                                <?php while ($role = $role_result->fetch_assoc()): ?>
                                    <option value="<?= $role['role_id'] ?>" <?= (isset($_POST['role_id']) && $_POST['role_id'] == $role['role_id']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($role['role_name']) ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="status" name="status" <?= (isset($_POST['status']) || !isset($_POST)) ? 'checked' : '' ?> />
                                <label class="form-check-label" for="status">Kích hoạt tài khoản</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">Thêm người dùng</button>
                        <a href="<?= ADMIN_URL ?>/users.php" class="btn btn-outline-secondary">Hủy bỏ</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>