/* Custom CSS for Restaurant Management System - Modern UI/UX */

:root {
  --primary-color: #1266f1;
  --secondary-color: #b23cfd;
  --success-color: #00b74a;
  --info-color: #39c0ed;
  --warning-color: #ffa900;
  --danger-color: #f93154;
  --light-color: #fbfbfb;
  --dark-color: #262626;
  --background-color: #f5f5f5;
  --card-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  --transition-speed: 0.3s;
}

body {
  background-color: var(--background-color);
  font-family: 'Roboto', 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
  color: #4f4f4f;
  font-size: 16px;
  line-height: 1.6;
}

/* Modern Card Styling */
.card {
  border-radius: 0.5rem;
  border: none;
  box-shadow: var(--card-shadow);
  transition: transform var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
  margin-bottom: 1.5rem;
}

.card:hover {
  box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.1), 0 6px 20px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  background-color: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  border-top-left-radius: 0.5rem !important;
  border-top-right-radius: 0.5rem !important;
  padding: 1rem 1.5rem;
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  background-color: white;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem 1.5rem;
}

/* Table Cards */
.table-card {
  height: 100%;
  transition: all var(--transition-speed) ease;
}

.table-available {
  border-left: 4px solid var(--success-color);
}

.table-occupied {
  border-left: 4px solid var(--danger-color);
}

.table-reserved {
  border-left: 4px solid var(--warning-color);
}

.table-maintenance {
  border-left: 4px solid var(--info-color);
}

/* Food Item Cards */
.food-item-card {
  transition: transform var(--transition-speed) ease;
}

.food-item-card:hover {
  transform: translateY(-5px);
}

.food-item-image {
  height: 160px;
  object-fit: cover;
}

.category-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 0.7rem;
  padding: 0.35rem 0.5rem;
  border-radius: 4px;
}

/* Status Dots */
.status-dot {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 6px;
}

.status-available {
  background-color: var(--success-color);
}

.status-occupied {
  background-color: var(--danger-color);
}

.status-reserved {
  background-color: var(--warning-color);
}

.status-maintenance {
  background-color: var(--info-color);
}

/* Typography Improvements */
h1, h2, h3, h4, h5, h6 {
  font-weight: 500;
  margin-bottom: 1rem;
  color: var(--dark-color);
}

h1 {
  font-size: 2.2rem;
}

h2 {
  font-size: 1.8rem;
}

h3 {
  font-size: 1.5rem;
}

.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Form Styling */
.form-outline .form-control:focus ~ .form-label {
  color: var(--primary-color);
}

.form-outline .form-control.active ~ .form-label, 
.form-outline .form-control:focus ~ .form-label {
  transform: translateY(-1rem) translateY(0.1rem) scale(0.8);
}

.form-outline .form-control {
  border-radius: 0.25rem;
  padding: 0.33rem 0.75rem;
  min-height: calc(2.5rem);
}

.form-select {
  min-height: calc(2.5rem);
}

/* Button Improvements */
.btn {
  border-radius: 0.25rem;
  text-transform: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  box-shadow: none;
  transition: all var(--transition-speed) ease;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.2), 0 4px 20px 0 rgba(0, 0, 0, 0.1);
}

.btn-floating {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Breadcrumb Styling */
.breadcrumb {
  background-color: transparent;
  padding: 0.75rem 0;
}

.breadcrumb-item + .breadcrumb-item::before {
  content: "\f105";
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

/* Navigation */
.navbar {
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
}

.navbar-nav .nav-link {
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: all var(--transition-speed) ease;
}

.navbar-nav .nav-link:hover {
  color: var(--primary-color);
}

.navbar-nav .nav-link.active {
  color: var(--primary-color);
}

/* Table Styling */
.table {
  border-collapse: separate;
  border-spacing: 0;
}

.table thead th {
  border-top: none;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.8rem;
  letter-spacing: 0.03em;
  color: #757575;
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Badge Styling */
.badge {
  font-weight: 500;
  padding: 0.35rem 0.65rem;
  border-radius: 0.25rem;
}

/* Responsive Adjustments */
@media (max-width: 767px) {
  .card-body {
    padding: 1rem;
  }
  
  h1 {
    font-size: 1.8rem;
  }
  
  h2 {
    font-size: 1.5rem;
  }
  
  .d-flex {
    flex-wrap: wrap;
  }
  
  .btn {
    margin-bottom: 0.5rem;
  }
}

/* Toast notifications */
.toast-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1100;
}

.toast {
  box-shadow: var(--card-shadow);
  border: none;
  border-radius: 0.25rem;
  min-width: 250px;
}

/* Loading Spinner */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
}

/* Animation for content loading */
.fade-in {
  animation: fadeIn var(--transition-speed) ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Skeleton Loading Effect */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 0.25rem;
  height: 20px;
  margin-bottom: 10px;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Order management specific styles */
.order-status-badge {
  font-size: 0.8rem;
  padding: 0.35rem 0.65rem;
}

/* Dashboard Cards */
.dashboard-card {
  transition: all var(--transition-speed) ease;
  border-radius: 0.5rem;
  overflow: hidden;
  border: none;
  box-shadow: var(--card-shadow);
}

.dashboard-card:hover {
  transform: translateY(-5px);
}

.dashboard-card .card-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1.5rem;
}

.dashboard-card .icon-bg {
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

/* Custom tooltip */
.custom-tooltip {
  position: relative;
  display: inline-block;
}

.custom-tooltip .tooltip-text {
  visibility: hidden;
  width: 120px;
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity var(--transition-speed) ease;
}

.custom-tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}