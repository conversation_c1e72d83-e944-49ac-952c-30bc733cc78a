<?php
require_once '../includes/header.php';

// Check if user is staff
if (!isStaff()) {
    setFlashMessage('danger', 'Bạn không có quyền truy cập trang này');
    redirect(SITE_URL);
}

// Check if table_id is provided
if (!isset($_GET['table_id']) || empty($_GET['table_id'])) {
    setFlashMessage('danger', '<PERSON>ui lòng chọn bàn để tạo đơn hàng');
    redirect(STAFF_URL . '/tables.php');
}

$table_id = (int)$_GET['table_id'];

// Get table information
$table_sql = "SELECT * FROM tables WHERE table_id = ?";
$table_stmt = $conn->prepare($table_sql);
$table_stmt->bind_param("i", $table_id);
$table_stmt->execute();
$table_result = $table_stmt->get_result();

if ($table_result->num_rows == 0) {
    setFlashMessage('danger', 'Không tìm thấy thông tin bàn');
    redirect(STAFF_URL . '/tables.php');
}

$table = $table_result->fetch_assoc();

// Check if table is in maintenance
if ($table['status'] == 'maintenance') {
    setFlashMessage('danger', 'Bàn này đang bảo trì, không thể tạo đơn hàng');
    redirect(STAFF_URL . '/tables.php');
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $discount_percent = isset($_POST['discount_percent']) ? (float)$_POST['discount_percent'] : 0;
    $notes = sanitize($_POST['notes'] ?? '');
    $error = '';
    
    // Check if any items are selected
    if (!isset($_POST['quantity']) || empty($_POST['quantity'])) {
        $error = 'Vui lòng chọn ít nhất một món ăn';
    } else {
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Create new order
            $order_sql = "INSERT INTO orders (table_id, user_id, discount_percent, notes) 
                          VALUES (?, ?, ?, ?)";
            $order_stmt = $conn->prepare($order_sql);
            $user_id = $_SESSION['user_id'];
            $order_stmt->bind_param("iids", $table_id, $user_id, $discount_percent, $notes);
            $order_stmt->execute();
            $order_id = $conn->insert_id;
            
            // Add order details
            $quantities = $_POST['quantity'];
            $prices = $_POST['price'];
            
            foreach ($quantities as $food_id => $quantity) {
                if ($quantity > 0) {
                    $price = $prices[$food_id];
                    $subtotal = $quantity * $price;
                    
                    $detail_sql = "INSERT INTO order_details (order_id, food_id, quantity, unit_price, subtotal) 
                                  VALUES (?, ?, ?, ?, ?)";
                    $detail_stmt = $conn->prepare($detail_sql);
                    $detail_stmt->bind_param("iiidi", $order_id, $food_id, $quantity, $price, $subtotal);
                    $detail_stmt->execute();
                }
            }
            
            // Update table status to occupied
            $update_table_sql = "UPDATE tables SET status = 'occupied' WHERE table_id = ?";
            $update_table_stmt = $conn->prepare($update_table_sql);
            $update_table_stmt->bind_param("i", $table_id);
            $update_table_stmt->execute();
            
            // Commit transaction
            $conn->commit();
            
            setFlashMessage('success', 'Đơn hàng đã được tạo thành công');
            redirect(STAFF_URL . '/edit_order.php?id=' . $order_id);
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $error = 'Đã xảy ra lỗi khi tạo đơn hàng: ' . $e->getMessage();
        }
    }
}

// Get all active food items grouped by category
$food_sql = "SELECT f.*, c.category_name FROM food_items f 
            JOIN categories c ON f.category_id = c.category_id 
            WHERE f.status = 1 AND c.status = 1
            ORDER BY c.category_name, f.food_name";
$food_result = $conn->query($food_sql);

// Group food items by category
$categories = [];
while ($food = $food_result->fetch_assoc()) {
    $category_name = $food['category_name'];
    if (!isset($categories[$category_name])) {
        $categories[$category_name] = [
            'category_id' => $food['category_id'],
            'items' => []
        ];
    }
    $categories[$category_name]['items'][] = $food;
}

// Get all categories for the filter
$category_sql = "SELECT category_id, category_name FROM categories WHERE status = 1 ORDER BY category_name";
$category_result = $conn->query($category_sql);
?>

<div class="row mb-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= STAFF_URL ?>">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="<?= STAFF_URL ?>/tables.php">Quản lý bàn</a></li>
                <li class="breadcrumb-item active" aria-current="page">Tạo đơn hàng mới</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <h2>Tạo đơn hàng mới</h2>
    </div>
    <div class="col-md-6 text-end">
        <h5>Bàn: <?= htmlspecialchars($table['table_number']) ?> (<?= $table['capacity'] ?> người)</h5>
    </div>
</div>

<?php if (isset($error) && !empty($error)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?= $error ?>
        <button type="button" class="btn-close" data-mdb-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<form method="post" action="">
    <div class="row">
        <!-- Food Items Selection -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Chọn món ăn</h5>
                </div>
                <div class="card-body">
                    <!-- Category Filter Buttons -->
                    <div class="mb-4">
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-primary active" onclick="filterFoodByCategory(0)">Tất cả</button>
                            <?php while ($category = $category_result->fetch_assoc()): ?>
                                <button type="button" class="btn btn-outline-primary" onclick="filterFoodByCategory(<?= $category['category_id'] ?>)">
                                    <?= htmlspecialchars($category['category_name']) ?>
                                </button>
                            <?php endwhile; ?>
                        </div>
                    </div>
                    
                    <!-- Food Items Grid -->
                    <div class="row">
                        <?php foreach ($categories as $category_name => $category): ?>
                            <?php foreach ($category['items'] as $food): ?>
                                <div class="col-md-4 mb-4 food-item-card" data-category="<?= $food['category_id'] ?>">
                                    <div class="card h-100">
                                        <?php if (!empty($food['image_path']) && file_exists('../' . $food['image_path'])): ?>
                                            <img src="<?= SITE_URL . '/' . $food['image_path'] ?>" class="card-img-top food-item-image" alt="<?= htmlspecialchars($food['food_name']) ?>">
                                        <?php else: ?>
                                            <div class="bg-light d-flex align-items-center justify-content-center food-item-image">
                                                <i class="fas fa-utensils fa-3x text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="card-body">
                                            <h5 class="card-title"><?= htmlspecialchars($food['food_name']) ?></h5>
                                            <p class="card-text text-primary fw-bold"><?= number_format($food['price'], 0, ',', '.') ?> đ</p>
                                            <button type="button" class="btn btn-primary btn-sm w-100" onclick="addItemToOrder(<?= $food['food_id'] ?>, '<?= addslashes($food['food_name']) ?>', <?= $food['price'] ?>)">
                                                <i class="fas fa-plus me-1"></i>Thêm
                                            </button>
                                        </div>
                                        
                                        <span class="badge bg-info category-badge"><?= htmlspecialchars($category_name) ?></span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Order Summary -->
        <div class="col-md-4">
            <div class="card position-sticky" style="top: 1rem;">
                <div class="card-header">
                    <h5 class="mb-0">Chi tiết đơn hàng</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="fw-bold mb-2">Món đã chọn:</div>
                        <div id="orderItems">
                            <!-- Order items will be added here by JavaScript -->
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="mb-3">
                        <div class="row mb-2">
                            <div class="col-6">Tổng tiền:</div>
                            <div class="col-6 text-end" id="subtotal">0 đ</div>
                            <input type="hidden" id="subtotalInput" name="subtotal" value="0">
                        </div>
                        
                        <div class="row mb-2">
                            <div class="col-6">
                                <label for="discountPercent" class="form-label">Giảm giá (%):</label>
                                <input type="number" class="form-control form-control-sm" id="discountPercent" name="discount_percent" min="0" max="100" value="0">
                            </div>
                            <div class="col-6 text-end align-self-end" id="discountAmount">0 đ</div>
                            <input type="hidden" id="discountAmountInput" name="discount_amount" value="0">
                        </div>
                        
                        <div class="row fw-bold">
                            <div class="col-6">Thành tiền:</div>
                            <div class="col-6 text-end" id="finalAmount">0 đ</div>
                            <input type="hidden" id="finalAmountInput" name="final_amount" value="0">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Ghi chú:</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>Lưu đơn hàng
                        </button>
                        <a href="<?= STAFF_URL ?>/tables.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Quay lại
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<?php require_once '../includes/footer.php'; ?>