<?php
require_once '../includes/config.php';

// Set the content type to JSON
header('Content-Type: application/json');

// Verify user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Get requested data type
$data_type = isset($_GET['type']) ? $_GET['type'] : '';
$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];

// Default response structure
$response = [
    'success' => true,
    'timestamp' => date('Y-m-d H:i:s'),
    'data' => null
];

// Handle different data requests based on user role and request type
switch ($data_type) {
    case 'revenue':
        // Get date range
        $start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-6 days'));
        $end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
        
        // Validate dates
        if (!validateDate($start_date) || !validateDate($end_date)) {
            $response['success'] = false;
            $response['message'] = 'Invalid date format';
            break;
        }
        
        // Query for revenue data
        if (isAdmin()) {
            // For admin, get overall revenue
            $query = "SELECT DATE(payment_date) as date, SUM(payment_amount) as total 
                     FROM payments 
                     WHERE payment_date BETWEEN ? AND ? 
                     AND payment_status = 'completed' 
                     GROUP BY DATE(payment_date) 
                     ORDER BY date ASC";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("ss", $start_date, $end_date);
        } else {
            // For staff, get only their revenue
            $query = "SELECT DATE(payment_date) as date, SUM(payment_amount) as total 
                     FROM payments 
                     WHERE payment_date BETWEEN ? AND ? 
                     AND received_by = ? 
                     AND payment_status = 'completed' 
                     GROUP BY DATE(payment_date) 
                     ORDER BY date ASC";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("ssi", $start_date, $end_date, $user_id);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        
        // Format data for chart
        $chart_data = [];
        
        // Initialize with all dates in range
        $date_period = new DatePeriod(
            new DateTime($start_date),
            new DateInterval('P1D'),
            (new DateTime($end_date))->modify('+1 day')
        );
        
        foreach ($date_period as $date) {
            $formatted_date = $date->format('Y-m-d');
            $chart_data[$formatted_date] = 0;
        }
        
        // Fill in actual data
        while ($row = $result->fetch_assoc()) {
            $chart_data[$row['date']] = (float) $row['total'];
        }
        
        // Calculate total and average
        $total_revenue = array_sum($chart_data);
        $avg_revenue = count($chart_data) > 0 ? $total_revenue / count($chart_data) : 0;
        
        // Format response
        $response['data'] = [
            'labels' => array_map(function($date) { 
                return date('d/m', strtotime($date)); 
            }, array_keys($chart_data)),
            'values' => array_values($chart_data),
            'total' => $total_revenue,
            'average' => $avg_revenue,
            'period' => [
                'start' => $start_date,
                'end' => $end_date,
                'days' => count($chart_data)
            ]
        ];
        break;
        
    case 'tables':
        // Get table status data
        $table_status_query = "SELECT status, COUNT(*) as count FROM tables GROUP BY status";
        $table_status_result = $conn->query($table_status_query);
        $table_status = [
            'available' => 0,
            'occupied' => 0,
            'reserved' => 0,
            'maintenance' => 0
        ];
        
        while ($row = $table_status_result->fetch_assoc()) {
            $table_status[$row['status']] = (int) $row['count'];
        }
        
        // Get specific tables if requested
        if (isset($_GET['details']) && $_GET['details'] === 'true') {
            $tables_query = "SELECT t.table_id, t.table_number, t.status, t.capacity, 
                           (SELECT COUNT(*) FROM orders WHERE table_id = t.table_id AND status IN ('pending', 'processing')) as has_active_order
                           FROM tables t
                           ORDER BY t.table_number ASC";
            $tables_result = $conn->query($tables_query);
            $tables = [];
            
            while ($table = $tables_result->fetch_assoc()) {
                $tables[] = $table;
            }
            
            $response['data'] = [
                'summary' => $table_status,
                'tables' => $tables
            ];
        } else {
            $response['data'] = $table_status;
        }
        break;
        
    case 'orders':
        // Get order status counts
        $order_status_query = "SELECT status, COUNT(*) as count FROM orders GROUP BY status";
        $order_status_result = $conn->query($order_status_query);
        $order_status = [
            'pending' => 0,
            'processing' => 0,
            'completed' => 0,
            'cancelled' => 0
        ];
        
        while ($row = $order_status_result->fetch_assoc()) {
            $order_status[$row['status']] = (int) $row['count'];
        }
        
        // Get today's order count
        $today = date('Y-m-d');
        $today_orders_query = "SELECT COUNT(*) as count FROM orders WHERE DATE(order_date) = ?";
        $today_orders_stmt = $conn->prepare($today_orders_query);
        $today_orders_stmt->bind_param("s", $today);
        $today_orders_stmt->execute();
        $today_orders_result = $today_orders_stmt->get_result();
        $today_orders_count = $today_orders_result->fetch_assoc()['count'];
        
        // If staff, also get their personal order count
        $staff_orders_count = 0;
        if ($user_role === 'staff') {
            $staff_orders_query = "SELECT COUNT(*) as count FROM orders WHERE DATE(order_date) = ? AND user_id = ?";
            $staff_orders_stmt = $conn->prepare($staff_orders_query);
            $staff_orders_stmt->bind_param("si", $today, $user_id);
            $staff_orders_stmt->execute();
            $staff_orders_result = $staff_orders_stmt->get_result();
            $staff_orders_count = $staff_orders_result->fetch_assoc()['count'];
        }
        
        $response['data'] = [
            'status_counts' => $order_status,
            'today_total' => (int) $today_orders_count,
            'staff_today' => (int) $staff_orders_count,
            'active' => $order_status['pending'] + $order_status['processing']
        ];
        break;
        
    case 'popular_items':
        // Get popular items
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 5;
        $limit = min($limit, 20); // Cap at 20 to prevent excessive queries
        
        $popular_query = "SELECT f.food_id, f.food_name, COUNT(od.order_detail_id) as order_count, 
                        SUM(od.quantity) as quantity_sum, SUM(od.price * od.quantity) as revenue
                        FROM order_details od
                        JOIN food_items f ON od.food_id = f.food_id
                        JOIN orders o ON od.order_id = o.order_id
                        WHERE o.status = 'completed'
                        GROUP BY f.food_id
                        ORDER BY order_count DESC
                        LIMIT ?";
        $popular_stmt = $conn->prepare($popular_query);
        $popular_stmt->bind_param("i", $limit);
        $popular_stmt->execute();
        $popular_result = $popular_stmt->get_result();
        
        $popular_items = [];
        while ($item = $popular_result->fetch_assoc()) {
            $popular_items[] = [
                'id' => (int) $item['food_id'],
                'name' => $item['food_name'],
                'order_count' => (int) $item['order_count'],
                'quantity_sold' => (int) $item['quantity_sum'],
                'revenue' => (float) $item['revenue']
            ];
        }
        
        $response['data'] = $popular_items;
        break;
        
    case 'notifications':
        // Get notifications for current user
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
        $limit = min($limit, 50); // Cap at 50
        
        $notifications_query = "SELECT notification_id, message, type, created_at, is_read 
                              FROM notifications 
                              WHERE user_id = ? 
                              ORDER BY created_at DESC 
                              LIMIT ?";
        $notifications_stmt = $conn->prepare($notifications_query);
        $notifications_stmt->bind_param("ii", $user_id, $limit);
        $notifications_stmt->execute();
        $notifications_result = $notifications_stmt->get_result();
        
        $notifications = [];
        while ($notification = $notifications_result->fetch_assoc()) {
            $notifications[] = [
                'id' => (int) $notification['notification_id'],
                'message' => $notification['message'],
                'type' => $notification['type'],
                'created_at' => $notification['created_at'],
                'is_read' => (bool) $notification['is_read']
            ];
        }
        
        // Get unread count
        $unread_query = "SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0";
        $unread_stmt = $conn->prepare($unread_query);
        $unread_stmt->bind_param("i", $user_id);
        $unread_stmt->execute();
        $unread_result = $unread_stmt->get_result();
        $unread_count = $unread_result->fetch_assoc()['count'];
        
        $response['data'] = [
            'notifications' => $notifications,
            'unread_count' => (int) $unread_count
        ];
        break;
        
    default:
        $response['success'] = false;
        $response['message'] = 'Invalid data type requested';
        break;
}

// Return the JSON response
echo json_encode($response);

// Helper function to validate date format
function validateDate($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}