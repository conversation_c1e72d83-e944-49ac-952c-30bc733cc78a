<?php
require_once '../includes/header.php';

// Check if user is admin
if (!isAdmin()) {
    setFlashMessage('danger', 'Bạn không có quyền truy cập trang này');
    redirect(SITE_URL);
}

// Get date range for analytics
$end_date = date('Y-m-d');
$start_date = date('Y-m-d', strtotime('-6 days'));

if (isset($_GET['date_range'])) {
    switch ($_GET['date_range']) {
        case 'today':
            $start_date = $end_date;
            break;
        case 'week':
            $start_date = date('Y-m-d', strtotime('-6 days'));
            break;
        case 'month':
            $start_date = date('Y-m-d', strtotime('-29 days'));
            break;
        case 'year':
            $start_date = date('Y-m-d', strtotime('-364 days'));
            break;
        case 'custom':
            if (isset($_GET['start_date']) && isset($_GET['end_date'])) {
                $custom_start = date('Y-m-d', strtotime($_GET['start_date']));
                $custom_end = date('Y-m-d', strtotime($_GET['end_date']));
                
                if ($custom_start <= $custom_end) {
                    $start_date = $custom_start;
                    $end_date = $custom_end;
                }
            }
            break;
    }
}

// Get statistics
// Count total food items
$food_query = "SELECT COUNT(*) as total FROM food_items WHERE status = 1";
$food_result = $conn->query($food_query);
$food_count = $food_result->fetch_assoc()['total'];

// Count total categories
$category_query = "SELECT COUNT(*) as total FROM categories WHERE status = 1";
$category_result = $conn->query($category_query);
$category_count = $category_result->fetch_assoc()['total'];

// Count tables by status
$table_query = "SELECT status, COUNT(*) as count FROM tables GROUP BY status";
$table_result = $conn->query($table_query);
$table_status = [
    'available' => 0,
    'occupied' => 0,
    'reserved' => 0,
    'maintenance' => 0
];
while ($table = $table_result->fetch_assoc()) {
    $table_status[$table['status']] = $table['count'];
}
$table_count = array_sum($table_status);

// Count total users
$user_query = "SELECT role, COUNT(*) as count FROM users WHERE status = 1 GROUP BY role";
$user_result = $conn->query($user_query);
$user_roles = [
    'admin' => 0,
    'staff' => 0,
    'customer' => 0
];
while ($user = $user_result->fetch_assoc()) {
    $user_roles[$user['role']] = $user['count'];
}
$user_count = array_sum($user_roles);

// Get today's revenue
$today = date('Y-m-d');
$revenue_query = "SELECT SUM(payment_amount) as total FROM payments 
                  WHERE DATE(payment_date) = ? AND payment_status = 'completed'";
$revenue_stmt = $conn->prepare($revenue_query);
$revenue_stmt->bind_param("s", $today);
$revenue_stmt->execute();
$revenue_result = $revenue_stmt->get_result();
$today_revenue = $revenue_result->fetch_assoc()['total'] ?? 0;

// Get weekly revenue data for chart
$weekly_revenue_query = "SELECT DATE(payment_date) as date, SUM(payment_amount) as total 
                       FROM payments 
                       WHERE payment_date BETWEEN ? AND ? AND payment_status = 'completed' 
                       GROUP BY DATE(payment_date) 
                       ORDER BY date ASC";
$weekly_revenue_stmt = $conn->prepare($weekly_revenue_query);
$weekly_revenue_stmt->bind_param("ss", $start_date, $end_date);
$weekly_revenue_stmt->execute();
$weekly_revenue_result = $weekly_revenue_stmt->get_result();

$chart_labels = [];
$chart_data = [];
$total_period_revenue = 0;

// Initialize with all dates in range (including days with 0 revenue)
$date_period = new DatePeriod(
    new DateTime($start_date),
    new DateInterval('P1D'),
    (new DateTime($end_date))->modify('+1 day')
);

foreach ($date_period as $date) {
    $formatted_date = $date->format('Y-m-d');
    $chart_labels[] = $date->format('d/m');
    $chart_data[$formatted_date] = 0;
}

// Fill in actual data
while ($row = $weekly_revenue_result->fetch_assoc()) {
    $chart_data[$row['date']] = (float) $row['total'];
    $total_period_revenue += (float) $row['total'];
}

// Convert to sequential array for Chart.js
$chart_values = array_values($chart_data);

// Get order status counts
$order_status_query = "SELECT status, COUNT(*) as count FROM orders GROUP BY status";
$order_status_result = $conn->query($order_status_query);
$order_status = [
    'pending' => 0,
    'processing' => 0,
    'completed' => 0,
    'cancelled' => 0
];

while ($status = $order_status_result->fetch_assoc()) {
    $order_status[$status['status']] = $status['count'];
}

// Calculate some additional metrics
$order_count_query = "SELECT COUNT(*) as count FROM orders WHERE order_date BETWEEN ? AND ?";
$order_count_stmt = $conn->prepare($order_count_query);
$order_count_stmt->bind_param("ss", $start_date, $end_date);
$order_count_stmt->execute();
$order_count_result = $order_count_stmt->get_result();
$period_order_count = $order_count_result->fetch_assoc()['count'];

$avg_order_value = $period_order_count > 0 ? $total_period_revenue / $period_order_count : 0;

// Get popular categories
$popular_categories_query = "SELECT c.category_name, COUNT(od.order_detail_id) as order_count 
                           FROM order_details od 
                           JOIN food_items f ON od.food_id = f.food_id 
                           JOIN categories c ON f.category_id = c.category_id 
                           JOIN orders o ON od.order_id = o.order_id 
                           WHERE o.status = 'completed' 
                           GROUP BY c.category_id 
                           ORDER BY order_count DESC 
                           LIMIT 5";
$popular_categories_result = $conn->query($popular_categories_query);

$category_labels = [];
$category_data = [];

while ($category = $popular_categories_result->fetch_assoc()) {
    $category_labels[] = $category['category_name'];
    $category_data[] = $category['order_count'];
}

// Get recent orders
$orders_query = "SELECT o.order_id, o.order_date, o.final_amount, o.status, 
                t.table_number, u.username
                FROM orders o
                JOIN tables t ON o.table_id = t.table_id
                JOIN users u ON o.user_id = u.user_id
                ORDER BY o.order_date DESC LIMIT 5";
$orders_result = $conn->query($orders_query);

// Get popular items
$popular_query = "SELECT f.food_name, COUNT(od.order_detail_id) as order_count, 
                SUM(od.price * od.quantity) as revenue
                FROM order_details od
                JOIN food_items f ON od.food_id = f.food_id
                JOIN orders o ON od.order_id = o.order_id
                WHERE o.status = 'completed'
                GROUP BY f.food_id
                ORDER BY order_count DESC
                LIMIT 5";
$popular_result = $conn->query($popular_query);

// Get recent activity log
$activity_query = "SELECT al.activity_id, al.activity_type, al.description, al.activity_time, u.username 
                 FROM activity_log al
                 LEFT JOIN users u ON al.user_id = u.user_id
                 ORDER BY al.activity_time DESC
                 LIMIT 10";
$activity_result = $conn->query($activity_query);
?>

<div class="dashboard-header">
    <div class="row align-items-center mb-4">
        <div class="col-md-6">
            <h2 class="mb-0">
                <i class="material-icons text-primary me-2">dashboard</i>
                Dashboard
            </h2>
            <p class="text-muted mb-0">Tổng quan hoạt động nhà hàng</p>
        </div>
        <div class="col-md-6">
            <div class="d-flex justify-content-md-end">
                <div class="dropdown">
                    <button class="btn btn-outline-primary dropdown-toggle d-flex align-items-center" type="button" id="dateRangeDropdown" data-mdb-toggle="dropdown" aria-expanded="false">
                        <i class="material-icons me-2">date_range</i>
                        <?php
                        if ($start_date === $end_date) {
                            echo 'Hôm nay';
                        } elseif ($start_date === date('Y-m-d', strtotime('-6 days'))) {
                            echo '7 ngày qua';
                        } elseif ($start_date === date('Y-m-d', strtotime('-29 days'))) {
                            echo '30 ngày qua';
                        } elseif ($start_date === date('Y-m-d', strtotime('-364 days'))) {
                            echo '1 năm qua';
                        } else {
                            echo date('d/m/Y', strtotime($start_date)) . ' - ' . date('d/m/Y', strtotime($end_date));
                        }
                        ?>
                    </button>
                    <ul class="dropdown-menu shadow-sm" aria-labelledby="dateRangeDropdown">
                        <li><a class="dropdown-item d-flex align-items-center <?= $start_date === $end_date ? 'active' : '' ?>" href="?date_range=today">
                            <i class="material-icons me-2">today</i>Hôm nay
                        </a></li>
                        <li><a class="dropdown-item d-flex align-items-center <?= $start_date === date('Y-m-d', strtotime('-6 days')) ? 'active' : '' ?>" href="?date_range=week">
                            <i class="material-icons me-2">view_week</i>7 ngày qua
                        </a></li>
                        <li><a class="dropdown-item d-flex align-items-center <?= $start_date === date('Y-m-d', strtotime('-29 days')) ? 'active' : '' ?>" href="?date_range=month">
                            <i class="material-icons me-2">calendar_month</i>30 ngày qua
                        </a></li>
                        <li><a class="dropdown-item d-flex align-items-center <?= $start_date === date('Y-m-d', strtotime('-364 days')) ? 'active' : '' ?>" href="?date_range=year">
                            <i class="material-icons me-2">calendar_today</i>1 năm qua
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <div class="dropdown-item d-flex align-items-center">
                                <i class="material-icons me-2">date_range</i>Tùy chỉnh
                                <button class="btn btn-sm btn-primary ms-2" data-mdb-toggle="modal" data-mdb-target="#customDateModal">
                                    Chọn
                                </button>
                            </div>
                        </li>
                    </ul>
                </div>
                <a href="reports.php" class="btn btn-primary ms-2 d-flex align-items-center">
                    <i class="material-icons me-2">analytics</i>
                    Báo cáo chi tiết
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Custom Date Range Modal -->
<div class="modal fade" id="customDateModal" tabindex="-1" aria-labelledby="customDateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customDateModalLabel">Chọn khoảng thời gian</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="customDateForm" action="" method="get">
                    <input type="hidden" name="date_range" value="custom">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-outline datepicker">
                                <input type="date" id="start_date" name="start_date" class="form-control" value="<?= $start_date ?>">
                                <label for="start_date" class="form-label">Từ ngày</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-outline datepicker">
                                <input type="date" id="end_date" name="end_date" class="form-control" value="<?= $end_date ?>">
                                <label for="end_date" class="form-label">Đến ngày</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-mdb-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" onclick="document.getElementById('customDateForm').submit()">Áp dụng</button>
            </div>
        </div>
    </div>
</div>

<!-- Key Performance Indicators -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card h-100 bg-white border-start border-primary border-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div class="dashboard-card-title">Doanh thu</div>
                    <div class="dashboard-icon bg-primary bg-opacity-10 text-primary">
                        <i class="material-icons">payments</i>
                    </div>
                </div>
                <div class="dashboard-card-value"><?= number_format($today_revenue, 0, ',', '.') ?> đ</div>
                <div class="dashboard-card-subtitle">Hôm nay</div>
                <div class="progress mt-3" style="height: 6px;">
                    <div class="progress-bar bg-primary" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <div class="dashboard-card-stats d-flex justify-content-between mt-3">
                    <div>
                        <div class="dashboard-card-stats-value text-primary"><?= number_format($total_period_revenue, 0, ',', '.') ?> đ</div>
                        <div class="dashboard-card-stats-label">Tổng <?= count($chart_labels) ?> ngày</div>
                    </div>
                    <div>
                        <div class="dashboard-card-stats-value text-primary"><?= number_format($avg_order_value, 0, ',', '.') ?> đ</div>
                        <div class="dashboard-card-stats-label">Giá trị TB/đơn</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card h-100 bg-white border-start border-success border-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div class="dashboard-card-title">Đơn hàng</div>
                    <div class="dashboard-icon bg-success bg-opacity-10 text-success">
                        <i class="material-icons">receipt_long</i>
                    </div>
                </div>
                <div class="dashboard-card-value"><?= $period_order_count ?></div>
                <div class="dashboard-card-subtitle"><?= count($chart_labels) ?> ngày qua</div>
                <div class="progress mt-3" style="height: 6px;">
                    <div class="progress-bar bg-success" role="progressbar" style="width: 75%;" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <div class="dashboard-card-stats d-flex justify-content-between mt-3">
                    <div>
                        <div class="dashboard-card-stats-value text-success"><?= $order_status['completed'] ?></div>
                        <div class="dashboard-card-stats-label">Hoàn thành</div>
                    </div>
                    <div>
                        <div class="dashboard-card-stats-value text-warning"><?= $order_status['pending'] + $order_status['processing'] ?></div>
                        <div class="dashboard-card-stats-label">Đang xử lý</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card h-100 bg-white border-start border-info border-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div class="dashboard-card-title">Bàn ăn</div>
                    <div class="dashboard-icon bg-info bg-opacity-10 text-info">
                        <i class="material-icons">table_restaurant</i>
                    </div>
                </div>
                <div class="dashboard-card-value"><?= $table_count ?></div>
                <div class="dashboard-card-subtitle">Tổng số bàn</div>
                <div class="progress mt-3" style="height: 6px;">
                    <div class="progress-bar bg-info" role="progressbar" style="width: <?= ($table_status['occupied'] / max(1, $table_count)) * 100 ?>%;" aria-valuenow="<?= ($table_status['occupied'] / max(1, $table_count)) * 100 ?>" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <div class="dashboard-card-stats d-flex justify-content-between mt-3">
                    <div>
                        <div class="dashboard-card-stats-value text-success"><?= $table_status['available'] ?></div>
                        <div class="dashboard-card-stats-label">Trống</div>
                    </div>
                    <div>
                        <div class="dashboard-card-stats-value text-danger"><?= $table_status['occupied'] ?></div>
                        <div class="dashboard-card-stats-label">Đang sử dụng</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card h-100 bg-white border-start border-warning border-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div class="dashboard-card-title">Món ăn</div>
                    <div class="dashboard-icon bg-warning bg-opacity-10 text-warning">
                        <i class="material-icons">restaurant_menu</i>
                    </div>
                </div>
                <div class="dashboard-card-value"><?= $food_count ?></div>
                <div class="dashboard-card-subtitle">Trong <?= $category_count ?> danh mục</div>
                <div class="progress mt-3" style="height: 6px;">
                    <div class="progress-bar bg-warning" role="progressbar" style="width: 85%;" aria-valuenow="85" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <div class="dashboard-card-stats d-flex justify-content-between mt-3">
                    <div>
                        <div class="dashboard-card-stats-value text-warning"><?= $category_count ?></div>
                        <div class="dashboard-card-stats-label">Danh mục</div>
                    </div>
                    <div>
                        <div class="dashboard-card-stats-value text-primary"><?= $user_count ?></div>
                        <div class="dashboard-card-stats-label">Người dùng</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts & Analysis -->
<div class="row mb-4">
    <!-- Revenue Chart -->
    <div class="col-lg-8 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center bg-transparent">
                <h5 class="mb-0">
                    <i class="material-icons text-primary me-2">trending_up</i>
                    Biểu đồ doanh thu
                </h5>
                <div class="chart-actions">
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary active" id="revenueChartBarView">
                            <i class="material-icons">bar_chart</i>
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="revenueChartLineView">
                            <i class="material-icons">show_chart</i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <canvas id="revenueChart" height="250"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Order Status -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">
                    <i class="material-icons text-success me-2">pie_chart</i>
                    Trạng thái đơn hàng
                </h5>
            </div>
            <div class="card-body d-flex flex-column justify-content-between">
                <div class="chart-container position-relative" style="height: 250px;">
                    <canvas id="orderStatusChart"></canvas>
                </div>
                <div class="chart-legend mt-3">
                    <div class="row">
                        <div class="col-6 mb-2">
                            <div class="d-flex align-items-center">
                                <div class="color-dot bg-success me-2"></div>
                                <div>
                                    <div class="small text-muted">Hoàn thành</div>
                                    <div class="fw-bold"><?= $order_status['completed'] ?></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-2">
                            <div class="d-flex align-items-center">
                                <div class="color-dot bg-warning me-2"></div>
                                <div>
                                    <div class="small text-muted">Chờ xử lý</div>
                                    <div class="fw-bold"><?= $order_status['pending'] ?></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="d-flex align-items-center">
                                <div class="color-dot bg-info me-2"></div>
                                <div>
                                    <div class="small text-muted">Đang xử lý</div>
                                    <div class="fw-bold"><?= $order_status['processing'] ?></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="d-flex align-items-center">
                                <div class="color-dot bg-danger me-2"></div>
                                <div>
                                    <div class="small text-muted">Đã hủy</div>
                                    <div class="fw-bold"><?= $order_status['cancelled'] ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Table Status & Popular Items -->
<div class="row mb-4">
    <!-- Table Status -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">
                    <i class="material-icons text-info me-2">table_restaurant</i>
                    Trạng thái bàn
                </h5>
            </div>
            <div class="card-body">
                <div class="row tables-overview mb-3">
                    <div class="col-6 mb-3">
                        <div class="table-status-card bg-success bg-opacity-10 text-success">
                            <div class="table-status-card-icon">
                                <i class="material-icons">check_circle</i>
                            </div>
                            <div class="table-status-card-info">
                                <h2 class="mb-0"><?= $table_status['available'] ?></h2>
                                <p class="mb-0">Trống</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="table-status-card bg-danger bg-opacity-10 text-danger">
                            <div class="table-status-card-icon">
                                <i class="material-icons">people</i>
                            </div>
                            <div class="table-status-card-info">
                                <h2 class="mb-0"><?= $table_status['occupied'] ?></h2>
                                <p class="mb-0">Đang sử dụng</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="table-status-card bg-warning bg-opacity-10 text-warning">
                            <div class="table-status-card-icon">
                                <i class="material-icons">event_busy</i>
                            </div>
                            <div class="table-status-card-info">
                                <h2 class="mb-0"><?= $table_status['reserved'] ?></h2>
                                <p class="mb-0">Đã đặt trước</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="table-status-card bg-secondary bg-opacity-10 text-secondary">
                            <div class="table-status-card-icon">
                                <i class="material-icons">build</i>
                            </div>
                            <div class="table-status-card-info">
                                <h2 class="mb-0"><?= $table_status['maintenance'] ?></h2>
                                <p class="mb-0">Bảo trì</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-grid">
                    <a href="tables.php" class="btn btn-outline-primary d-flex align-items-center justify-content-center">
                        <i class="material-icons me-2">visibility</i>
                        Xem chi tiết
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Popular Categories -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">
                    <i class="material-icons text-warning me-2">category</i>
                    Danh mục phổ biến
                </h5>
            </div>
            <div class="card-body">
                <?php if (count($category_labels) > 0): ?>
                    <div class="chart-container position-relative" style="height: 220px;">
                        <canvas id="categoryChart"></canvas>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="material-icons" style="font-size: 3rem; color: #ccc;">insert_chart</i>
                        <p class="mt-3 text-muted">Chưa có dữ liệu</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Popular Items -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-transparent d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="material-icons text-primary me-2">restaurant_menu</i>
                    Món ăn phổ biến
                </h5>
                <a href="food_items.php" class="btn btn-sm btn-outline-primary d-flex align-items-center">
                    <i class="material-icons me-1">list</i>Tất cả
                </a>
            </div>
            <div class="card-body px-0 pb-0">
                <?php if ($popular_result->num_rows > 0): ?>
                    <div class="list-group list-group-flush">
                        <?php while ($item = $popular_result->fetch_assoc()): ?>
                            <div class="list-group-item border-0 d-flex justify-content-between align-items-center px-3">
                                <div class="me-auto">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle bg-primary bg-opacity-10 text-primary me-3">
                                            <i class="material-icons">restaurant</i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?= htmlspecialchars($item['food_name']) ?></h6>
                                            <small class="text-muted"><?= number_format($item['revenue'], 0, ',', '.') ?> đ</small>
                                        </div>
                                    </div>
                                </div>
                                <span class="badge rounded-pill bg-primary bg-opacity-10 text-primary px-3 py-2">
                                    <?= $item['order_count'] ?> lần
                                </span>
                            </div>
                        <?php endwhile; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="material-icons" style="font-size: 3rem; color: #ccc;">restaurant_menu</i>
                        <p class="mt-3 text-muted">Chưa có dữ liệu</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Recent Orders & Activity Log -->
<div class="row">
    <!-- Recent Orders -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header bg-transparent d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="material-icons text-primary me-2">receipt_long</i>
                    Đơn hàng gần đây
                </h5>
                <a href="reports.php" class="btn btn-sm btn-outline-primary d-flex align-items-center">
                    <i class="material-icons me-1">visibility</i>Xem tất cả
                </a>
            </div>
            <div class="card-body px-0 pb-2">
                <?php if ($orders_result->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th class="ps-3">ID</th>
                                    <th>Bàn</th>
                                    <th>Nhân viên</th>
                                    <th>Ngày</th>
                                    <th>Tổng tiền</th>
                                    <th class="pe-3">Trạng thái</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($order = $orders_result->fetch_assoc()): ?>
                                    <tr>
                                        <td class="ps-3">
                                            <a href="view_order.php?id=<?= $order['order_id'] ?>" class="fw-bold text-primary">
                                                #<?= $order['order_id'] ?>
                                            </a>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle avatar-xs bg-info bg-opacity-10 text-info me-2">
                                                    <i class="material-icons">table_restaurant</i>
                                                </div>
                                                <span>Bàn <?= htmlspecialchars($order['table_number']) ?></span>
                                            </div>
                                        </td>
                                        <td><?= htmlspecialchars($order['username']) ?></td>
                                        <td><?= date('d/m/Y H:i', strtotime($order['order_date'])) ?></td>
                                        <td class="fw-bold"><?= number_format($order['final_amount'], 0, ',', '.') ?> đ</td>
                                        <td class="pe-3">
                                            <?php
                                            $status_class = '';
                                            $status_text = '';
                                            $status_icon = '';
                                            
                                            switch ($order['status']) {
                                                case 'pending':
                                                    $status_class = 'warning';
                                                    $status_text = 'Chờ xử lý';
                                                    $status_icon = 'pending';
                                                    break;
                                                case 'processing':
                                                    $status_class = 'info';
                                                    $status_text = 'Đang xử lý';
                                                    $status_icon = 'hourglass_empty';
                                                    break;
                                                case 'completed':
                                                    $status_class = 'success';
                                                    $status_text = 'Hoàn thành';
                                                    $status_icon = 'check_circle';
                                                    break;
                                                case 'cancelled':
                                                    $status_class = 'danger';
                                                    $status_text = 'Đã hủy';
                                                    $status_icon = 'cancel';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge bg-<?= $status_class ?> bg-opacity-10 text-<?= $status_class ?> px-3 py-2 rounded-pill d-flex align-items-center">
                                                <i class="material-icons me-1 fs-6"><?= $status_icon ?></i>
                                                <?= $status_text ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="material-icons" style="font-size: 3rem; color: #ccc;">receipt_long</i>
                        <p class="mt-3 text-muted">Không có đơn hàng nào</p>
                        <a href="../staff/new_order.php" class="btn btn-primary">Tạo đơn hàng mới</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Activity Log -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">
                    <i class="material-icons text-danger me-2">history</i>
                    Nhật ký hoạt động
                </h5>
            </div>
            <div class="card-body p-0">
                <?php if (isset($activity_result) && $activity_result->num_rows > 0): ?>
                    <div class="timeline-container p-3">
                        <?php while ($activity = $activity_result->fetch_assoc()): ?>
                            <div class="timeline-item">
                                <div class="timeline-dot bg-primary"></div>
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-title"><?= htmlspecialchars($activity['activity_type']) ?></div>
                                        <div class="timeline-date"><?= date('d/m/Y H:i', strtotime($activity['activity_time'])) ?></div>
                                    </div>
                                    <div class="timeline-body">
                                        <?= htmlspecialchars($activity['description']) ?>
                                    </div>
                                    <div class="timeline-footer text-muted">
                                        <small>Bởi: <?= htmlspecialchars($activity['username'] ?? 'System') ?></small>
                                    </div>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="material-icons" style="font-size: 3rem; color: #ccc;">history</i>
                        <p class="mt-3 text-muted">Chưa có hoạt động nào được ghi lại</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const revenueChartCtx = document.getElementById('revenueChart').getContext('2d');
    const revenueChart = new Chart(revenueChartCtx, {
        type: 'bar',
        data: {
            labels: <?= json_encode($chart_labels) ?>,
            datasets: [{
                label: 'Doanh thu (đ)',
                data: <?= json_encode($chart_values) ?>,
                backgroundColor: 'rgba(13, 110, 253, 0.4)',
                borderColor: 'rgba(13, 110, 253, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                label += new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(context.parsed.y);
                            }
                            return label;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            if (value >= 1000000) {
                                return (value / 1000000).toFixed(1) + 'M';
                            } else if (value >= 1000) {
                                return (value / 1000).toFixed(0) + 'K';
                            }
                            return value;
                        }
                    }
                }
            }
        }
    });
    
    // Toggle chart type
    document.getElementById('revenueChartBarView').addEventListener('click', function() {
        this.classList.add('active');
        document.getElementById('revenueChartLineView').classList.remove('active');
        revenueChart.config.type = 'bar';
        revenueChart.update();
    });
    
    document.getElementById('revenueChartLineView').addEventListener('click', function() {
        this.classList.add('active');
        document.getElementById('revenueChartBarView').classList.remove('active');
        revenueChart.config.type = 'line';
        revenueChart.update();
    });
    
    // Order Status Chart
    const orderStatusCtx = document.getElementById('orderStatusChart').getContext('2d');
    new Chart(orderStatusCtx, {
        type: 'doughnut',
        data: {
            labels: ['Hoàn thành', 'Chờ xử lý', 'Đang xử lý', 'Đã hủy'],
            datasets: [{
                data: [
                    <?= $order_status['completed'] ?>, 
                    <?= $order_status['pending'] ?>, 
                    <?= $order_status['processing'] ?>, 
                    <?= $order_status['cancelled'] ?>
                ],
                backgroundColor: [
                    'rgba(25, 135, 84, 0.8)',
                    'rgba(255, 193, 7, 0.8)',
                    'rgba(13, 202, 240, 0.8)',
                    'rgba(220, 53, 69, 0.8)'
                ],
                borderColor: [
                    'rgba(25, 135, 84, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(13, 202, 240, 1)',
                    'rgba(220, 53, 69, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            cutout: '70%'
        }
    });
    
    // Category Chart
    <?php if (count($category_labels) > 0): ?>
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    new Chart(categoryCtx, {
        type: 'polarArea',
        data: {
            labels: <?= json_encode($category_labels) ?>,
            datasets: [{
                data: <?= json_encode($category_data) ?>,
                backgroundColor: [
                    'rgba(13, 110, 253, 0.7)',
                    'rgba(25, 135, 84, 0.7)',
                    'rgba(255, 193, 7, 0.7)',
                    'rgba(220, 53, 69, 0.7)',
                    'rgba(13, 202, 240, 0.7)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 12,
                        font: {
                            size: 11
                        }
                    }
                }
            },
            scales: {
                r: {
                    ticks: {
                        display: false
                    }
                }
            }
        }
    });
    <?php endif; ?>
});
</script>

<style>
/* Dashboard Styles */
.dashboard-card {
    transition: transform 0.3s, box-shadow 0.3s;
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
}

.dashboard-card-title {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.dashboard-card-value {
    font-size: 1.75rem;
    font-weight: 700;
    margin-top: 5px;
}

.dashboard-card-subtitle {
    font-size: 0.8rem;
    color: #6c757d;
}

.dashboard-card-stats {
    font-size: 0.85rem;
}

.dashboard-card-stats-value {
    font-weight: 600;
}

.dashboard-card-stats-label {
    font-size: 0.75rem;
    color: #6c757d;
}

.dashboard-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.table-status-card {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 10px;
    height: 100%;
}

.table-status-card-icon {
    margin-right: 15px;
}

.table-status-card-info h2 {
    font-size: 1.5rem;
    font-weight: 700;
}

.table-status-card-info p {
    font-size: 0.85rem;
    margin-bottom: 0;
}

.color-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-circle.avatar-xs {
    width: 30px;
    height: 30px;
}

.avatar-circle i {
    font-size: 1.25rem;
}

.avatar-circle.avatar-xs i {
    font-size: 1rem;
}

/* Timeline styling */
.timeline-container {
    position: relative;
    max-height: 400px;
    overflow-y: auto;
}

.timeline-item {
    position: relative;
    padding-left: 25px;
    padding-bottom: 20px;
}

.timeline-item:last-child {
    padding-bottom: 0;
}

.timeline-dot {
    position: absolute;
    left: 0;
    top: 4px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-item:not(:last-child):before {
    content: '';
    position: absolute;
    left: 5px;
    top: 16px;
    bottom: 0;
    width: 2px;
    background-color: #e9ecef;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.timeline-title {
    font-weight: 600;
    font-size: 0.9rem;
}

.timeline-date {
    font-size: 0.75rem;
    color: #6c757d;
}

.timeline-body {
    font-size: 0.85rem;
    margin-bottom: 5px;
}

.timeline-footer {
    font-size: 0.75rem;
}
</style>

<?php require_once '../includes/footer.php'; ?>