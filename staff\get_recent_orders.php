<?php
require_once '../includes/config.php';

// Verify user is logged in and is staff
if (!isStaff()) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Get staff ID
$staff_id = $_SESSION['user_id'];

// Get recent orders by the current staff
$recent_orders_query = "SELECT o.order_id, o.order_date, o.final_amount, o.status, o.table_id, t.table_number, 
                      (SELECT COUNT(*) FROM order_details WHERE order_id = o.order_id) as item_count
                      FROM orders o
                      JOIN tables t ON o.table_id = t.table_id
                      WHERE o.user_id = ?
                      ORDER BY o.order_date DESC LIMIT 5";
$recent_orders_stmt = $conn->prepare($recent_orders_query);
$recent_orders_stmt->bind_param("i", $staff_id);
$recent_orders_stmt->execute();
$recent_orders_result = $recent_orders_stmt->get_result();

// Start capturing HTML output
ob_start();

if ($recent_orders_result->num_rows > 0): ?>
    <div class="list-group list-group-flush">
        <?php while ($order = $recent_orders_result->fetch_assoc()): 
            $status_class = '';
            $status_text = '';
            $status_icon = '';
            
            switch ($order['status']) {
                case 'pending':
                    $status_class = 'warning';
                    $status_text = 'Chờ xử lý';
                    $status_icon = 'pending';
                    break;
                case 'processing':
                    $status_class = 'info';
                    $status_text = 'Đang xử lý';
                    $status_icon = 'hourglass_empty';
                    break;
                case 'completed':
                    $status_class = 'success';
                    $status_text = 'Hoàn thành';
                    $status_icon = 'check_circle';
                    break;
                case 'cancelled':
                    $status_class = 'danger';
                    $status_text = 'Đã hủy';
                    $status_icon = 'cancel';
                    break;
            }
            
            // Calculate time elapsed
            $order_time = new DateTime($order['order_date']);
            $now = new DateTime();
            $interval = $now->diff($order_time);
            
            if ($interval->d > 0) {
                $time_elapsed = $interval->d . ' ngày trước';
            } elseif ($interval->h > 0) {
                $time_elapsed = $interval->h . ' giờ trước';
            } elseif ($interval->i > 0) {
                $time_elapsed = $interval->i . ' phút trước';
            } else {
                $time_elapsed = 'Vừa xong';
            }
        ?>
            <div class="list-group-item p-3 recent-order-item">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="badge rounded-pill bg-<?= $status_class ?> bg-opacity-10 text-<?= $status_class ?> px-3 py-2">
                                <i class="material-icons me-1 fs-6"><?= $status_icon ?></i>
                                <?= $status_text ?>
                            </span>
                        </div>
                        <div>
                            <h6 class="mb-0">Đơn #<?= $order['order_id'] ?></h6>
                            <div class="text-muted small">
                                <i class="material-icons fs-6 align-middle">access_time</i> 
                                <?= $time_elapsed ?>
                            </div>
                        </div>
                    </div>
                    <div class="text-end">
                        <div class="fw-bold"><?= number_format($order['final_amount'], 0, ',', '.') ?> đ</div>
                        <div class="text-muted small"><?= $order['item_count'] ?> món</div>
                    </div>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center text-muted">
                        <i class="material-icons me-1 fs-6">table_restaurant</i>
                        Bàn <?= htmlspecialchars($order['table_number']) ?>
                    </div>
                    <div class="order-actions">
                        <?php if ($order['status'] == 'pending' || $order['status'] == 'processing'): ?>
                            <a href="edit_order.php?id=<?= $order['order_id'] ?>" class="btn btn-sm btn-primary">
                                <i class="material-icons">edit</i>
                            </a>
                            <a href="payment.php?id=<?= $order['order_id'] ?>" class="btn btn-sm btn-success">
                                <i class="material-icons">payments</i>
                            </a>
                        <?php else: ?>
                            <a href="view_order.php?id=<?= $order['order_id'] ?>" class="btn btn-sm btn-info">
                                <i class="material-icons">visibility</i>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endwhile; ?>
    </div>
<?php else: ?>
    <div class="text-center py-5">
        <i class="material-icons" style="font-size: 3rem; color: #ccc;">receipt_long</i>
        <p class="mt-3 text-muted">Bạn chưa tạo đơn hàng nào hôm nay</p>
        <a href="new_order.php" class="btn btn-primary">
            <i class="material-icons me-2">add_circle</i>
            Tạo đơn hàng mới
        </a>
    </div>
<?php endif;

// Get the captured HTML
$html = ob_get_clean();

// Return JSON response
echo json_encode([
    'success' => true,
    'html' => $html,
    'count' => $recent_orders_result->num_rows
]);