<?php
require_once '../includes/header.php';

// Check if user is admin
if (!isAdmin()) {
    setFlashMessage('danger', 'Bạn không có quyền truy cập trang này');
    redirect(SITE_URL);
}

// Handle delete request
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $table_id = (int)$_GET['delete'];
    
    // Check if there are orders for this table
    $check_sql = "SELECT COUNT(*) as count FROM orders WHERE table_id = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("i", $table_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    $order_count = $check_result->fetch_assoc()['count'];
    
    if ($order_count > 0) {
        setFlashMessage('danger', 'Không thể xóa bàn này vì có ' . $order_count . ' đơn hàng liên quan');
    } else {
        // Delete the table
        $delete_sql = "DELETE FROM tables WHERE table_id = ?";
        $delete_stmt = $conn->prepare($delete_sql);
        $delete_stmt->bind_param("i", $table_id);
        
        if ($delete_stmt->execute()) {
            setFlashMessage('success', 'Bàn đã được xóa thành công');
        } else {
            setFlashMessage('danger', 'Không thể xóa bàn: ' . $conn->error);
        }
    }
    
    redirect(ADMIN_URL . '/tables.php');
}

// Handle add/edit table
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $table_number = sanitize($_POST['table_number']);
    $capacity = (int)$_POST['capacity'];
    $status = sanitize($_POST['status']);
    $table_id = isset($_POST['table_id']) ? (int)$_POST['table_id'] : 0;
    $error = '';
    
    // Validate inputs
    if (empty($table_number)) {
        $error = 'Vui lòng nhập số bàn';
    } elseif ($capacity <= 0) {
        $error = 'Sức chứa phải lớn hơn 0';
    } else {
        // Check if table number already exists (except for updating the current table)
        $check_sql = "SELECT table_id FROM tables WHERE table_number = ? AND table_id != ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("si", $table_number, $table_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            $error = 'Số bàn đã tồn tại';
        } else {
            if ($table_id > 0) {
                // Update existing table
                $sql = "UPDATE tables SET table_number = ?, capacity = ?, status = ? WHERE table_id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("sisi", $table_number, $capacity, $status, $table_id);
                $action = 'cập nhật';
            } else {
                // Add new table
                $sql = "INSERT INTO tables (table_number, capacity, status) VALUES (?, ?, ?)";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("sis", $table_number, $capacity, $status);
                $action = 'thêm';
            }
            
            if ($stmt->execute()) {
                setFlashMessage('success', 'Bàn đã được ' . $action . ' thành công');
                redirect(ADMIN_URL . '/tables.php');
            } else {
                $error = 'Đã xảy ra lỗi khi ' . $action . ' bàn: ' . $conn->error;
            }
        }
    }
}

// Get table data if editing
$editing = false;
$table = [
    'table_id' => '',
    'table_number' => '',
    'capacity' => '',
    'status' => 'available'
];

if (isset($_GET['edit']) && !empty($_GET['edit'])) {
    $table_id = (int)$_GET['edit'];
    $sql = "SELECT * FROM tables WHERE table_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $table_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $table = $result->fetch_assoc();
        $editing = true;
    }
}

// Get all tables
$sql = "SELECT * FROM tables ORDER BY table_number";
$result = $conn->query($sql);
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h2>Quản lý bàn</h2>
    </div>
</div>

<div class="row">
    <!-- Table Form -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><?= $editing ? 'Sửa thông tin bàn' : 'Thêm bàn mới' ?></h5>
            </div>
            <div class="card-body">
                <?php if (isset($error) && !empty($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?= $error ?>
                        <button type="button" class="btn-close" data-mdb-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <form method="post" action="">
                    <?php if ($editing): ?>
                        <input type="hidden" name="table_id" value="<?= $table['table_id'] ?>">
                    <?php endif; ?>
                    
                    <div class="mb-4">
                        <div class="form-outline">
                            <input type="text" id="table_number" name="table_number" class="form-control" required value="<?= htmlspecialchars($table['table_number']) ?>" />
                            <label class="form-label" for="table_number">Số bàn</label>
                        </div>
                        <div class="form-text">Ví dụ: A1, B2, VIP1, etc.</div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-outline">
                            <input type="number" id="capacity" name="capacity" class="form-control" required min="1" value="<?= htmlspecialchars($table['capacity']) ?>" />
                            <label class="form-label" for="capacity">Sức chứa (người)</label>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="form-label">Trạng thái</label>
                        <select class="form-select" name="status" required>
                            <option value="available" <?= $table['status'] == 'available' ? 'selected' : '' ?>>Trống</option>
                            <option value="occupied" <?= $table['status'] == 'occupied' ? 'selected' : '' ?>>Đang sử dụng</option>
                            <option value="reserved" <?= $table['status'] == 'reserved' ? 'selected' : '' ?>>Đã đặt trước</option>
                            <option value="maintenance" <?= $table['status'] == 'maintenance' ? 'selected' : '' ?>>Bảo trì</option>
                        </select>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary"><?= $editing ? 'Cập nhật' : 'Thêm mới' ?></button>
                        <?php if ($editing): ?>
                            <a href="<?= ADMIN_URL ?>/tables.php" class="btn btn-outline-secondary">Hủy bỏ</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Tables List -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Danh sách bàn</h5>
            </div>
            <div class="card-body">
                <?php if ($result->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Số bàn</th>
                                    <th>Sức chứa</th>
                                    <th>Trạng thái</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($table_row = $result->fetch_assoc()): ?>
                                    <tr>
                                        <td><?= $table_row['table_id'] ?></td>
                                        <td><?= htmlspecialchars($table_row['table_number']) ?></td>
                                        <td><?= $table_row['capacity'] ?> người</td>
                                        <td>
                                            <?php
                                            $status_text = '';
                                            $status_class = '';
                                            
                                            switch ($table_row['status']) {
                                                case 'available':
                                                    $status_text = 'Trống';
                                                    $status_class = 'success';
                                                    break;
                                                case 'occupied':
                                                    $status_text = 'Đang sử dụng';
                                                    $status_class = 'danger';
                                                    break;
                                                case 'reserved':
                                                    $status_text = 'Đã đặt trước';
                                                    $status_class = 'warning';
                                                    break;
                                                case 'maintenance':
                                                    $status_text = 'Bảo trì';
                                                    $status_class = 'info';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge bg-<?= $status_class ?>"><?= $status_text ?></span>
                                        </td>
                                        <td>
                                            <a href="tables.php?edit=<?= $table_row['table_id'] ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="tables.php?delete=<?= $table_row['table_id'] ?>" class="btn btn-sm btn-danger btn-delete" onclick="return confirm('Bạn có chắc chắn muốn xóa bàn này?');">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-center">Chưa có bàn nào trong hệ thống</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>