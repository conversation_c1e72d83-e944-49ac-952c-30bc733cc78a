<?php
require_once '../includes/header.php';

// Check if user is admin
if (!isAdmin()) {
    setFlashMessage('danger', 'Bạn không có quyền truy cập trang này');
    redirect(SITE_URL);
}

// Handle delete request
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $category_id = (int)$_GET['delete'];
    
    // Check if there are food items in this category
    $check_sql = "SELECT COUNT(*) as count FROM food_items WHERE category_id = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("i", $category_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    $food_count = $check_result->fetch_assoc()['count'];
    
    if ($food_count > 0) {
        setFlashMessage('danger', 'Không thể xóa danh mục này vì có ' . $food_count . ' món ăn thuộc danh mục');
    } else {
        // Delete the category
        $delete_sql = "DELETE FROM categories WHERE category_id = ?";
        $delete_stmt = $conn->prepare($delete_sql);
        $delete_stmt->bind_param("i", $category_id);
        
        if ($delete_stmt->execute()) {
            setFlashMessage('success', 'Danh mục đã được xóa thành công');
        } else {
            setFlashMessage('danger', 'Không thể xóa danh mục: ' . $conn->error);
        }
    }
    
    redirect(ADMIN_URL . '/categories.php');
}

// Handle add/edit category
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $category_name = sanitize($_POST['category_name']);
    $description = sanitize($_POST['description']);
    $status = isset($_POST['status']) ? 1 : 0;
    $category_id = isset($_POST['category_id']) ? (int)$_POST['category_id'] : 0;
    $error = '';
    
    // Validate inputs
    if (empty($category_name)) {
        $error = 'Vui lòng nhập tên danh mục';
    } else {
        // Check if category name already exists (except for updating the current category)
        $check_sql = "SELECT category_id FROM categories WHERE category_name = ? AND category_id != ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("si", $category_name, $category_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            $error = 'Tên danh mục đã tồn tại';
        } else {
            if ($category_id > 0) {
                // Update existing category
                $sql = "UPDATE categories SET category_name = ?, description = ?, status = ? WHERE category_id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("ssii", $category_name, $description, $status, $category_id);
                $action = 'cập nhật';
            } else {
                // Add new category
                $sql = "INSERT INTO categories (category_name, description, status) VALUES (?, ?, ?)";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("ssi", $category_name, $description, $status);
                $action = 'thêm';
            }
            
            if ($stmt->execute()) {
                setFlashMessage('success', 'Danh mục đã được ' . $action . ' thành công');
                redirect(ADMIN_URL . '/categories.php');
            } else {
                $error = 'Đã xảy ra lỗi khi ' . $action . ' danh mục: ' . $conn->error;
            }
        }
    }
}

// Get category data if editing
$editing = false;
$category = [
    'category_id' => '',
    'category_name' => '',
    'description' => '',
    'status' => 1
];

if (isset($_GET['edit']) && !empty($_GET['edit'])) {
    $category_id = (int)$_GET['edit'];
    $sql = "SELECT * FROM categories WHERE category_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $category_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $category = $result->fetch_assoc();
        $editing = true;
    }
}

// Get all categories
$sql = "SELECT c.*, (SELECT COUNT(*) FROM food_items WHERE category_id = c.category_id) as food_count 
        FROM categories c ORDER BY c.category_name";
$result = $conn->query($sql);
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= ADMIN_URL ?>">Trang chủ</a></li>
        <li class="breadcrumb-item active" aria-current="page">Quản lý danh mục</li>
    </ol>
</nav>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">
        <i class="material-icons text-primary me-2">category</i>
        Quản lý danh mục
    </h2>
    <div class="d-flex gap-2">
        <!-- Search box -->
        <div class="input-group w-auto">
            <input type="text" class="form-control" placeholder="Tìm danh mục..." id="tableSearch" aria-label="Tìm kiếm danh mục">
            <button class="btn btn-primary search-button" type="button">
                <i class="material-icons">search</i>
            </button>
        </div>
        
        <!-- Status filter dropdown -->
        <div class="dropdown">
            <button class="btn btn-outline-primary dropdown-toggle" type="button" id="statusFilterDropdown" data-mdb-toggle="dropdown" aria-expanded="false">
                <i class="material-icons me-1">filter_list</i>
                Lọc
            </button>
            <ul class="dropdown-menu shadow-sm" aria-labelledby="statusFilterDropdown">
                <li><a class="dropdown-item active" href="#" onclick="filterTableRows('all')">Tất cả</a></li>
                <li><a class="dropdown-item" href="#" onclick="filterTableRows('active')">Đang kích hoạt</a></li>
                <li><a class="dropdown-item" href="#" onclick="filterTableRows('inactive')">Đã vô hiệu</a></li>
            </ul>
        </div>
    </div>
</div>

<div class="row">
    <!-- Category Form -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header bg-primary bg-opacity-10 d-flex justify-content-between align-items-center">
                <h5 class="mb-0 text-primary">
                    <i class="material-icons me-1"><?= $editing ? 'edit' : 'add_circle' ?></i>
                    <?= $editing ? 'Sửa danh mục' : 'Thêm danh mục mới' ?>
                </h5>
                <?php if ($editing): ?>
                    <a href="<?= ADMIN_URL ?>/categories.php" class="btn btn-sm btn-outline-primary">
                        <i class="material-icons fs-6">add</i> Thêm mới
                    </a>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if (isset($error) && !empty($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show d-flex align-items-center" role="alert">
                        <i class="material-icons me-2">error</i>
                        <div><?= $error ?></div>
                        <button type="button" class="btn-close" data-mdb-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <form method="post" action="">
                    <?php if ($editing): ?>
                        <input type="hidden" name="category_id" value="<?= $category['category_id'] ?>">
                    <?php endif; ?>
                    
                    <div class="mb-4">
                        <div class="form-outline">
                            <input type="text" id="category_name" name="category_name" class="form-control" required value="<?= htmlspecialchars($category['category_name']) ?>" />
                            <label class="form-label" for="category_name">Tên danh mục</label>
                        </div>
                        <div class="form-text">Tên danh mục sẽ hiển thị trên menu và các trang món ăn</div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-outline">
                            <textarea id="description" name="description" class="form-control" rows="4"><?= htmlspecialchars($category['description']) ?></textarea>
                            <label class="form-label" for="description">Mô tả</label>
                        </div>
                        <div class="form-text">Mô tả về danh mục sẽ giúp khách hàng dễ hiểu hơn</div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-check form-switch d-flex align-items-center gap-2">
                            <input class="form-check-input" type="checkbox" id="status" name="status" <?= $category['status'] == 1 ? 'checked' : '' ?> />
                            <label class="form-check-label" for="status">Kích hoạt danh mục</label>
                            <i class="material-icons fs-6 text-muted custom-tooltip">
                                help
                                <span class="tooltip-text">Chỉ hiển thị các danh mục được kích hoạt</span>
                            </i>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary d-flex align-items-center">
                            <i class="material-icons me-2"><?= $editing ? 'save' : 'add_circle' ?></i>
                            <?= $editing ? 'Cập nhật' : 'Thêm mới' ?>
                        </button>
                        <?php if ($editing): ?>
                            <a href="<?= ADMIN_URL ?>/categories.php" class="btn btn-outline-secondary d-flex align-items-center">
                                <i class="material-icons me-2">cancel</i>
                                Hủy bỏ
                            </a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Categories List -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="material-icons text-primary me-1">list</i>
                    Danh sách danh mục
                </h5>
                <span class="badge rounded-pill bg-primary bg-opacity-10 text-primary">
                    <?= $result->num_rows ?> danh mục
                </span>
            </div>
            <div class="card-body">
                <?php if ($result->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th scope="col" style="width: 60px;">ID</th>
                                    <th scope="col">Tên danh mục</th>
                                    <th scope="col">Mô tả</th>
                                    <th scope="col" style="width: 100px;">Số món ăn</th>
                                    <th scope="col" style="width: 100px;">Trạng thái</th>
                                    <th scope="col" style="width: 120px;">Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($category = $result->fetch_assoc()): ?>
                                    <tr data-status="<?= $category['status'] == 1 ? 'active' : 'inactive' ?>">
                                        <td><?= $category['category_id'] ?></td>
                                        <td>
                                            <div class="fw-bold"><?= htmlspecialchars($category['category_name']) ?></div>
                                        </td>
                                        <td class="text-truncate" style="max-width: 250px;">
                                            <?= htmlspecialchars($category['description']) ?>
                                        </td>
                                        <td class="text-center">
                                            <?php if ($category['food_count'] > 0): ?>
                                                <span class="badge rounded-pill bg-primary">
                                                    <?= $category['food_count'] ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="badge rounded-pill bg-light text-dark">
                                                    0
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($category['status'] == 1): ?>
                                                <span class="badge bg-success bg-opacity-10 text-success px-3 py-2 rounded-pill">
                                                    <i class="material-icons fs-6 me-1">check_circle</i>
                                                    Kích hoạt
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-danger bg-opacity-10 text-danger px-3 py-2 rounded-pill">
                                                    <i class="material-icons fs-6 me-1">cancel</i>
                                                    Vô hiệu
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                <a href="categories.php?edit=<?= $category['category_id'] ?>" class="btn btn-primary btn-floating btn-sm" data-mdb-toggle="tooltip" title="Sửa danh mục">
                                                    <i class="material-icons">edit</i>
                                                </a>
                                                <?php if ($category['food_count'] == 0): ?>
                                                    <a href="categories.php?delete=<?= $category['category_id'] ?>" class="btn btn-danger btn-floating btn-sm btn-delete" data-mdb-toggle="tooltip" title="Xóa danh mục">
                                                        <i class="material-icons">delete</i>
                                                    </a>
                                                <?php else: ?>
                                                    <button type="button" class="btn btn-danger btn-floating btn-sm" disabled data-mdb-toggle="tooltip" title="Không thể xóa danh mục có món ăn">
                                                        <i class="material-icons">delete</i>
                                                    </button>
                                                <?php endif; ?>
                                                <a href="food_items.php?category=<?= $category['category_id'] ?>" class="btn btn-info btn-floating btn-sm" data-mdb-toggle="tooltip" title="Xem món ăn trong danh mục">
                                                    <i class="material-icons">restaurant_menu</i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="material-icons" style="font-size: 4rem; color: #ccc;">category</i>
                        <p class="mt-3 text-muted">Chưa có danh mục nào trong hệ thống</p>
                        <button type="button" class="btn btn-primary" onclick="document.getElementById('category_name').focus()">
                            <i class="material-icons me-2">add_circle</i>
                            Thêm danh mục đầu tiên
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
// Table search and filtering functions
function filterTableRows(status) {
    const tableRows = document.querySelectorAll('table tbody tr');
    
    tableRows.forEach(row => {
        if (status === 'all' || row.dataset.status === status) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
    
    // Update active state on filter buttons
    document.querySelectorAll('#statusFilterDropdown + .dropdown-menu .dropdown-item').forEach(link => {
        link.classList.remove('active');
    });
    
    document.querySelector(`#statusFilterDropdown + .dropdown-menu .dropdown-item[onclick*="${status}"]`).classList.add('active');
}

document.addEventListener('DOMContentLoaded', function() {
    // Initialize table search
    const searchInput = document.getElementById('tableSearch');
    searchInput.addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('table tbody tr');
        
        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
    
    // Initialize delete confirmations
    document.querySelectorAll('.btn-delete').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const categoryName = this.closest('tr').querySelector('td:nth-child(2)').textContent.trim();
            
            if (confirm(`Bạn có chắc chắn muốn xóa danh mục "${categoryName}"?`)) {
                window.location.href = this.getAttribute('href');
            }
        });
    });
});
</script>

<?php require_once '../includes/footer.php'; ?>