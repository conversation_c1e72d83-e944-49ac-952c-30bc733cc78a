/* Modern Restaurant Management System Styles */
:root {
    --primary-color: #667eea;
    --primary-dark: #5a6fd8;
    --secondary-color: #764ba2;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --border-radius: 12px;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-family: 'Inter', 'Roboto', sans-serif;
    line-height: 1.6;
}

.login-container {
    max-width: 480px;
    margin: 0 auto;
    padding: 3rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.login-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.card {
    box-shadow: var(--shadow-md);
    margin-bottom: 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    background: white;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    font-weight: 600;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    border-bottom: 1px solid #e2e8f0;
    padding: 1.25rem 1.5rem;
}

.table-card {
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    background: white;
    border: 1px solid #e2e8f0;
}

.table-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.table-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.table-available::before {
    background: linear-gradient(90deg, var(--success-color), #34d399);
}

.table-occupied::before {
    background: linear-gradient(90deg, var(--danger-color), #f87171);
}

.table-reserved::before {
    background: linear-gradient(90deg, var(--warning-color), #fbbf24);
}

.table-maintenance::before {
    background: linear-gradient(90deg, var(--info-color), #60a5fa);
}

.table-card .card-body {
    padding: 1.5rem;
    position: relative;
    z-index: 1;
}

.food-item-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: var(--border-radius);
    overflow: hidden;
    background: white;
    border: 1px solid #e2e8f0;
}

.food-item-card:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: var(--shadow-lg);
}

.food-item-image {
    height: 180px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.food-item-card:hover .food-item-image {
    transform: scale(1.05);
}

.category-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: var(--shadow-sm);
}

.dashboard-card {
    height: 100%;
    background: white;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
    position: relative;
    overflow: hidden;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.dashboard-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.dashboard-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.report-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid #e2e8f0;
}

.status-dot {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    position: relative;
    animation: pulse 2s infinite;
}

.status-dot::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: inherit;
    opacity: 0.3;
    animation: ping 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes ping {
    0% { transform: scale(1); opacity: 0.3; }
    75%, 100% { transform: scale(1.5); opacity: 0; }
}

.status-available {
    background-color: var(--success-color);
}

.status-occupied {
    background-color: var(--danger-color);
}

.status-reserved {
    background-color: var(--warning-color);
}

.status-maintenance {
    background-color: var(--info-color);
}

/* Modern Button Styles */
.btn {
    border-radius: 8px;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #059669);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #dc2626);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

/* Enhanced Form Styles */
.form-control {
    border-radius: 8px;
    border: 2px solid #e2e8f0;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

/* Enhanced Navbar */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-sm);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.brand-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    box-shadow: var(--shadow-sm);
}

.brand-text {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: 'Poppins', sans-serif;
}

.nav-link {
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    border-radius: 8px;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
    color: var(--dark-color) !important;
}

.nav-link:hover {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color) !important;
    transform: translateY(-1px);
}

.nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white !important;
}

.user-avatar i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.user-name {
    font-weight: 600;
    color: var(--dark-color);
}

.user-role {
    font-size: 0.65rem;
    padding: 0.25rem 0.5rem;
}

.dropdown-menu {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: 0.5rem 0;
    min-width: 250px;
}

.dropdown-header {
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    margin-bottom: 0.5rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 0;
}

.dropdown-item:hover {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
    transform: translateX(5px);
}

.dropdown-divider {
    margin: 0.5rem 1rem;
    border-color: #e2e8f0;
}

/* Enhanced Badges */
.badge {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design Improvements */
@media (max-width: 768px) {
    .table-responsive {
        overflow-x: auto;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-sm);
    }
    
    .login-container {
        margin: 1rem;
        padding: 2rem;
    }
    
    .dashboard-card {
        margin-bottom: 1rem;
    }
    
    .table-card {
        margin-bottom: 1rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .dashboard-icon {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .container {
        padding: 0 1rem;
    }
}

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Enhanced Form Validation */
.form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Enhanced Tooltips */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--dark-color);
    border-radius: var(--border-radius);
    padding: 0.5rem 0.75rem;
}

/* Search Input Enhancement */
.search-input {
    position: relative;
}

.search-input::before {
    content: '\f002';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--muted-color);
    z-index: 10;
}

.search-input input {
    padding-left: 2.5rem;
}

/* Enhanced Modals */
.modal-content {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
}

/* Enhanced Tables */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table thead th {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 1rem;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

.table tbody td {
    padding: 1rem;
    border-color: #e9ecef;
}

/* Enhanced Pagination */
.pagination {
    gap: 0.25rem;
}

.page-link {
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    color: var(--primary-color);
    font-weight: 500;
    transition: all 0.3s ease;
}

.page-link:hover {
    background-color: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
    transform: translateY(-1px);
}

.page-item.active .page-link {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-color: var(--primary-color);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --card-bg: #2d2d2d;
        --text-color: #ffffff;
        --border-color: #404040;
    }
    
    body {
        background-color: var(--bg-color);
        color: var(--text-color);
    }
    
    .card {
        background-color: var(--card-bg);
        border-color: var(--border-color);
    }
    
    .navbar {
        background: rgba(45, 45, 45, 0.95) !important;
    }
    
    .table thead th {
        background: linear-gradient(135deg, #2d2d2d, #404040);
        color: var(--text-color);
    }
    
    .table tbody tr:hover {
        background-color: rgba(102, 126, 234, 0.1);
    }
}