<?php
require_once '../includes/config.php';

// Verify user is logged in and is staff
if (!isStaff()) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Get table status summary
$table_status_query = "SELECT status, COUNT(*) as count FROM tables GROUP BY status";
$table_status_result = $conn->query($table_status_query);
$table_status = [
    'available' => 0,
    'occupied' => 0,
    'reserved' => 0,
    'maintenance' => 0
];

while ($row = $table_status_result->fetch_assoc()) {
    $table_status[$row['status']] = $row['count'];
}

// Get recently modified tables
$recent_tables_query = "SELECT t.table_id, t.table_number, t.status, t.capacity, o.order_id, o.order_date 
                      FROM tables t 
                      LEFT JOIN orders o ON t.table_id = o.table_id AND o.status IN ('pending', 'processing')
                      ORDER BY 
                        CASE 
                          WHEN t.status = 'occupied' THEN 1 
                          WHEN t.status = 'reserved' THEN 2
                          WHEN t.status = 'available' THEN 3
                          ELSE 4 
                        END, 
                        o.order_date DESC 
                      LIMIT 4";
$recent_tables_result = $conn->query($recent_tables_query);

// Start capturing HTML output for recent tables
ob_start();

if ($recent_tables_result->num_rows > 0): ?>
    <div class="row g-2">
        <?php while ($table = $recent_tables_result->fetch_assoc()): 
            $status_class = '';
            $status_icon = '';
            
            switch ($table['status']) {
                case 'available':
                    $status_class = 'success';
                    $status_icon = 'check_circle';
                    break;
                case 'occupied':
                    $status_class = 'danger';
                    $status_icon = 'people';
                    break;
                case 'reserved':
                    $status_class = 'warning';
                    $status_icon = 'event_busy';
                    break;
                case 'maintenance':
                    $status_class = 'secondary';
                    $status_icon = 'build';
                    break;
            }
        ?>
            <div class="col-6">
                <div class="recent-table-card">
                    <div class="d-flex align-items-center">
                        <div class="table-icon bg-<?= $status_class ?> bg-opacity-10 text-<?= $status_class ?> me-3">
                            <i class="material-icons"><?= $status_icon ?></i>
                        </div>
                        <div class="table-info">
                            <h6 class="mb-1">Bàn <?= htmlspecialchars($table['table_number']) ?></h6>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-<?= $status_class ?> bg-opacity-10 text-<?= $status_class ?> me-2">
                                    <?= ucfirst($table['status']) ?>
                                </span>
                                <small class="text-muted"><?= $table['capacity'] ?> người</small>
                            </div>
                        </div>
                    </div>
                    <?php if ($table['status'] == 'occupied' && $table['order_id']): ?>
                        <div class="mt-2 text-end">
                            <a href="edit_order.php?id=<?= $table['order_id'] ?>" class="btn btn-sm btn-outline-primary">
                                <i class="material-icons">receipt</i> #<?= $table['order_id'] ?>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endwhile; ?>
    </div>
<?php else: ?>
    <div class="text-center py-3">
        <p class="text-muted">Không có dữ liệu bàn</p>
    </div>
<?php endif;

// Get the captured HTML
$tables_html = ob_get_clean();

// Return JSON response
echo json_encode([
    'success' => true,
    'status' => $table_status,
    'tables_html' => $tables_html,
    'timestamp' => date('Y-m-d H:i:s')
]);