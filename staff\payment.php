<?php
require_once '../includes/header.php';

// Check if user is staff
if (!isStaff()) {
    setFlashMessage('danger', 'Bạn không có quyền truy cập trang này');
    redirect(SITE_URL);
}

// Check if order ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    setFlashMessage('danger', 'ID đơn hàng không hợp lệ');
    redirect(STAFF_URL . '/tables.php');
}

$order_id = (int)$_GET['id'];

// Get order information
$order_sql = "SELECT o.*, t.table_number, t.table_id
              FROM orders o
              JOIN tables t ON o.table_id = t.table_id
              WHERE o.order_id = ?";
$order_stmt = $conn->prepare($order_sql);
$order_stmt->bind_param("i", $order_id);
$order_stmt->execute();
$order_result = $order_stmt->get_result();

if ($order_result->num_rows == 0) {
    setFlashMessage('danger', 'Không tìm thấy đơn hàng');
    redirect(STAFF_URL . '/tables.php');
}

$order = $order_result->fetch_assoc();

// Check if order is already paid
$payment_sql = "SELECT * FROM payments WHERE order_id = ?";
$payment_stmt = $conn->prepare($payment_sql);
$payment_stmt->bind_param("i", $order_id);
$payment_stmt->execute();
$payment_result = $payment_stmt->get_result();

if ($payment_result->num_rows > 0) {
    $payment = $payment_result->fetch_assoc();
    
    if ($payment['payment_status'] == 'completed') {
        setFlashMessage('warning', 'Đơn hàng này đã được thanh toán');
        redirect(STAFF_URL . '/view_order.php?id=' . $order_id);
    }
}

// Get order details
$details_sql = "SELECT od.*, f.food_name 
                FROM order_details od
                JOIN food_items f ON od.food_id = f.food_id
                WHERE od.order_id = ?";
$details_stmt = $conn->prepare($details_sql);
$details_stmt->bind_param("i", $order_id);
$details_stmt->execute();
$details_result = $details_stmt->get_result();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $payment_method = sanitize($_POST['payment_method']);
    $payment_amount = (float)$_POST['payment_amount'];
    $notes = sanitize($_POST['notes'] ?? '');
    $error = '';
    
    // Validate inputs
    if (empty($payment_method)) {
        $error = 'Vui lòng chọn phương thức thanh toán';
    } elseif ($payment_amount <= 0) {
        $error = 'Số tiền thanh toán phải lớn hơn 0';
    } elseif ($payment_amount < $order['final_amount']) {
        $error = 'Số tiền thanh toán phải lớn hơn hoặc bằng tổng số tiền đơn hàng';
    } else {
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Create payment record
            $payment_sql = "INSERT INTO payments (order_id, payment_amount, payment_method, received_by, notes, payment_status) 
                           VALUES (?, ?, ?, ?, ?, 'completed')";
            $payment_stmt = $conn->prepare($payment_sql);
            $user_id = $_SESSION['user_id'];
            $payment_stmt->bind_param("idsss", $order_id, $payment_amount, $payment_method, $user_id, $notes);
            $payment_stmt->execute();
            
            // Update order status to completed
            $update_order_sql = "UPDATE orders SET status = 'completed' WHERE order_id = ?";
            $update_order_stmt = $conn->prepare($update_order_sql);
            $update_order_stmt->bind_param("i", $order_id);
            $update_order_stmt->execute();
            
            // Update table status to available
            $update_table_sql = "UPDATE tables SET status = 'available' WHERE table_id = ?";
            $update_table_stmt = $conn->prepare($update_table_sql);
            $update_table_stmt->bind_param("i", $order['table_id']);
            $update_table_stmt->execute();
            
            // Commit transaction
            $conn->commit();
            
            setFlashMessage('success', 'Đơn hàng đã được thanh toán thành công');
            redirect(STAFF_URL . '/view_order.php?id=' . $order_id);
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $error = 'Đã xảy ra lỗi khi thanh toán: ' . $e->getMessage();
        }
    }
}
?>

<div class="row mb-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= STAFF_URL ?>">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="<?= STAFF_URL ?>/tables.php">Quản lý bàn</a></li>
                <li class="breadcrumb-item"><a href="<?= STAFF_URL ?>/edit_order.php?id=<?= $order_id ?>">Đơn hàng #<?= $order_id ?></a></li>
                <li class="breadcrumb-item active" aria-current="page">Thanh toán</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Chi tiết đơn hàng #<?= $order_id ?></h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>Bàn:</strong> <?= htmlspecialchars($order['table_number']) ?></p>
                        <p><strong>Ngày tạo:</strong> <?= date('d/m/Y H:i', strtotime($order['order_date'])) ?></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Trạng thái:</strong> 
                            <?php
                            switch ($order['status']) {
                                case 'pending':
                                    echo '<span class="badge bg-warning">Chờ xử lý</span>';
                                    break;
                                case 'processing':
                                    echo '<span class="badge bg-info">Đang xử lý</span>';
                                    break;
                                case 'completed':
                                    echo '<span class="badge bg-success">Hoàn thành</span>';
                                    break;
                                case 'cancelled':
                                    echo '<span class="badge bg-danger">Đã hủy</span>';
                                    break;
                            }
                            ?>
                        </p>
                        <?php if (!empty($order['notes'])): ?>
                            <p><strong>Ghi chú:</strong> <?= nl2br(htmlspecialchars($order['notes'])) ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Món ăn</th>
                                <th>Giá</th>
                                <th>Số lượng</th>
                                <th class="text-end">Thành tiền</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($detail = $details_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?= htmlspecialchars($detail['food_name']) ?></td>
                                    <td><?= number_format($detail['unit_price'], 0, ',', '.') ?> đ</td>
                                    <td><?= $detail['quantity'] ?></td>
                                    <td class="text-end"><?= number_format($detail['subtotal'], 0, ',', '.') ?> đ</td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="3" class="text-end"><strong>Tổng tiền:</strong></td>
                                <td class="text-end"><?= number_format($order['total_amount'], 0, ',', '.') ?> đ</td>
                            </tr>
                            <?php if ($order['discount_percent'] > 0): ?>
                                <tr>
                                    <td colspan="3" class="text-end"><strong>Giảm giá (<?= $order['discount_percent'] ?>%):</strong></td>
                                    <td class="text-end">-<?= number_format($order['discount_amount'], 0, ',', '.') ?> đ</td>
                                </tr>
                            <?php endif; ?>
                            <tr>
                                <td colspan="3" class="text-end"><strong>Thành tiền:</strong></td>
                                <td class="text-end"><strong><?= number_format($order['final_amount'], 0, ',', '.') ?> đ</strong></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Thanh toán</h5>
            </div>
            <div class="card-body">
                <?php if (isset($error) && !empty($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?= $error ?>
                        <button type="button" class="btn-close" data-mdb-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <form method="post" action="">
                    <div class="mb-4">
                        <label class="form-label">Tổng số tiền cần thanh toán</label>
                        <div class="form-control bg-light fs-4 text-primary fw-bold">
                            <?= number_format($order['final_amount'], 0, ',', '.') ?> đ
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="form-label" for="payment_amount">Số tiền khách trả</label>
                        <input type="number" id="payment_amount" name="payment_amount" class="form-control" required min="<?= $order['final_amount'] ?>" value="<?= $order['final_amount'] ?>" onchange="calculateChange()">
                    </div>
                    
                    <div class="mb-4">
                        <label class="form-label">Tiền thừa</label>
                        <div id="change_amount" class="form-control bg-light fs-4 text-success fw-bold">
                            0 đ
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="form-label">Phương thức thanh toán</label>
                        <select class="form-select" name="payment_method" required>
                            <option value="">Chọn phương thức thanh toán</option>
                            <option value="cash">Tiền mặt</option>
                            <option value="credit_card">Thẻ tín dụng</option>
                            <option value="debit_card">Thẻ ghi nợ</option>
                            <option value="mobile_payment">Thanh toán di động</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="form-label" for="notes">Ghi chú thanh toán</label>
                        <textarea id="notes" name="notes" class="form-control" rows="3"></textarea>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-check-circle me-2"></i>Xác nhận thanh toán
                        </button>
                        <a href="<?= STAFF_URL ?>/edit_order.php?id=<?= $order_id ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Quay lại
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function calculateChange() {
    const finalAmount = <?= $order['final_amount'] ?>;
    const paymentAmount = parseFloat(document.getElementById('payment_amount').value);
    const changeAmount = paymentAmount - finalAmount;
    
    document.getElementById('change_amount').textContent = changeAmount.toLocaleString('vi-VN') + ' đ';
    
    if (changeAmount < 0) {
        document.getElementById('change_amount').classList.remove('text-success');
        document.getElementById('change_amount').classList.add('text-danger');
    } else {
        document.getElementById('change_amount').classList.remove('text-danger');
        document.getElementById('change_amount').classList.add('text-success');
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    calculateChange();
});
</script>

<?php require_once '../includes/footer.php'; ?>