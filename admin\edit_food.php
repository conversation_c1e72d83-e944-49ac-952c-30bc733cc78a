<?php
require_once '../includes/header.php';

// Check if user is admin
if (!isAdmin()) {
    setFlashMessage('danger', 'Bạn không có quyền truy cập trang này');
    redirect(SITE_URL);
}

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    setFlashMessage('danger', 'ID món ăn không hợp lệ');
    redirect(ADMIN_URL . '/food_items.php');
}

$food_id = (int)$_GET['id'];

// Get food item data
$sql = "SELECT * FROM food_items WHERE food_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $food_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    setFlashMessage('danger', 'Không tìm thấy món ăn');
    redirect(ADMIN_URL . '/food_items.php');
}

$food = $result->fetch_assoc();

// Get all categories
$category_sql = "SELECT category_id, category_name FROM categories WHERE status = 1 ORDER BY category_name";
$category_result = $conn->query($category_sql);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $food_name = sanitize($_POST['food_name']);
    $category_id = (int)$_POST['category_id'];
    $price = (float)$_POST['price'];
    $description = sanitize($_POST['description']);
    $status = isset($_POST['status']) ? 1 : 0;
    $error = '';
    
    // Validate inputs
    if (empty($food_name)) {
        $error = 'Vui lòng nhập tên món ăn';
    } elseif ($category_id <= 0) {
        $error = 'Vui lòng chọn danh mục';
    } elseif ($price <= 0) {
        $error = 'Vui lòng nhập giá hợp lệ';
    } else {
        // Handle image upload
        $image_path = $food['image_path']; // Keep existing image by default
        
        if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
            $max_size = 2 * 1024 * 1024; // 2MB
            
            if (!in_array($_FILES['image']['type'], $allowed_types)) {
                $error = 'Chỉ chấp nhận file hình ảnh (JPEG, PNG, GIF)';
            } elseif ($_FILES['image']['size'] > $max_size) {
                $error = 'Kích thước file không được vượt quá 2MB';
            } else {
                // Create upload directory if it doesn't exist
                $upload_dir = '../assets/images/food/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }
                
                // Generate unique filename
                $filename = time() . '_' . preg_replace('/[^a-zA-Z0-9]/', '', $food_name) . '_' . basename($_FILES['image']['name']);
                $target_file = $upload_dir . $filename;
                
                if (move_uploaded_file($_FILES['image']['tmp_name'], $target_file)) {
                    // Delete old image if it exists
                    if (!empty($food['image_path']) && file_exists('../' . $food['image_path'])) {
                        unlink('../' . $food['image_path']);
                    }
                    
                    $image_path = 'assets/images/food/' . $filename;
                } else {
                    $error = 'Không thể tải lên hình ảnh';
                }
            }
        }
        
        if (empty($error)) {
            // Update food item
            $sql = "UPDATE food_items SET food_name = ?, category_id = ?, price = ?, 
                    description = ?, image_path = ?, status = ? WHERE food_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("sidssis", $food_name, $category_id, $price, $description, $image_path, $status, $food_id);
            
            if ($stmt->execute()) {
                setFlashMessage('success', 'Thông tin món ăn đã được cập nhật thành công');
                redirect(ADMIN_URL . '/food_items.php');
            } else {
                $error = 'Đã xảy ra lỗi khi cập nhật món ăn: ' . $conn->error;
            }
        }
    }
}
?>

<div class="row mb-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= ADMIN_URL ?>">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="<?= ADMIN_URL ?>/food_items.php">Món ăn</a></li>
                <li class="breadcrumb-item active" aria-current="page">Sửa món ăn</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Sửa thông tin món ăn</h5>
            </div>
            <div class="card-body">
                <?php if (isset($error) && !empty($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?= $error ?>
                        <button type="button" class="btn-close" data-mdb-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <form method="post" action="" enctype="multipart/form-data">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-outline">
                                <input type="text" id="food_name" name="food_name" class="form-control" required value="<?= htmlspecialchars($_POST['food_name'] ?? $food['food_name']) ?>" />
                                <label class="form-label" for="food_name">Tên món ăn</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <select class="form-select" name="category_id" required>
                                <option value="">Chọn danh mục</option>
                                <?php 
                                $category_result->data_seek(0); // Reset pointer
                                while ($category = $category_result->fetch_assoc()): 
                                    $selected = (isset($_POST['category_id']) && $_POST['category_id'] == $category['category_id']) || 
                                               (!isset($_POST['category_id']) && $food['category_id'] == $category['category_id']);
                                ?>
                                    <option value="<?= $category['category_id'] ?>" <?= $selected ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($category['category_name']) ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-outline">
                                <input type="number" id="price" name="price" class="form-control" required min="0" step="1000" value="<?= htmlspecialchars($_POST['price'] ?? $food['price']) ?>" />
                                <label class="form-label" for="price">Giá (đ)</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <?php 
                                $checked = (isset($_POST['status'])) || 
                                          (!isset($_POST['status']) && $food['status'] == 1);
                                ?>
                                <input class="form-check-input" type="checkbox" id="status" name="status" <?= $checked ? 'checked' : '' ?> />
                                <label class="form-check-label" for="status">Có sẵn</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-outline">
                            <textarea id="description" name="description" class="form-control" rows="4"><?= htmlspecialchars($_POST['description'] ?? $food['description']) ?></textarea>
                            <label class="form-label" for="description">Mô tả</label>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="form-label" for="image">Hình ảnh</label>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*" />
                        <div id="imageHelp" class="form-text">Chấp nhận file JPG, PNG, GIF (tối đa 2MB). Để trống nếu không muốn thay đổi hình ảnh.</div>
                    </div>
                    
                    <div class="mb-4">
                        <?php if (!empty($food['image_path']) && file_exists('../' . $food['image_path'])): ?>
                            <p>Hình ảnh hiện tại:</p>
                            <img src="<?= SITE_URL . '/' . $food['image_path'] ?>" alt="<?= htmlspecialchars($food['food_name']) ?>" style="max-width: 100%; max-height: 200px;" class="mb-3" />
                        <?php endif; ?>
                        
                        <img id="imagePreview" src="#" alt="Xem trước hình ảnh" style="max-width: 100%; max-height: 200px; display: none;" />
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">Cập nhật món ăn</button>
                        <a href="<?= ADMIN_URL ?>/food_items.php" class="btn btn-outline-secondary">Hủy bỏ</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Image preview
document.getElementById('image').addEventListener('change', function(e) {
    var preview = document.getElementById('imagePreview');
    preview.style.display = 'none';
    
    if (this.files && this.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(this.files[0]);
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>