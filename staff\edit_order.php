<?php
require_once '../includes/header.php';

// Check if user is staff
if (!isStaff()) {
    setFlashMessage('danger', 'Bạn không có quyền truy cập trang này');
    redirect(SITE_URL);
}

// Check if order ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    setFlashMessage('danger', 'ID đơn hàng không hợp lệ');
    redirect(STAFF_URL . '/tables.php');
}

$order_id = (int)$_GET['id'];

// Get order information
$order_sql = "SELECT o.*, t.table_number, t.capacity 
              FROM orders o
              JOIN tables t ON o.table_id = t.table_id
              WHERE o.order_id = ?";
$order_stmt = $conn->prepare($order_sql);
$order_stmt->bind_param("i", $order_id);
$order_stmt->execute();
$order_result = $order_stmt->get_result();

if ($order_result->num_rows == 0) {
    setFlashMessage('danger', 'Không tìm thấy đơn hàng');
    redirect(STAFF_URL . '/tables.php');
}

$order = $order_result->fetch_assoc();

// Check if order is editable (pending or processing)
if ($order['status'] != 'pending' && $order['status'] != 'processing') {
    setFlashMessage('danger', 'Không thể chỉnh sửa đơn hàng đã hoàn thành hoặc đã hủy');
    redirect(STAFF_URL . '/tables.php');
}

// Get order details
$details_sql = "SELECT od.*, f.food_name 
                FROM order_details od
                JOIN food_items f ON od.food_id = f.food_id
                WHERE od.order_id = ?";
$details_stmt = $conn->prepare($details_sql);
$details_stmt->bind_param("i", $order_id);
$details_stmt->execute();
$details_result = $details_stmt->get_result();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $discount_percent = isset($_POST['discount_percent']) ? (float)$_POST['discount_percent'] : 0;
    $notes = sanitize($_POST['notes'] ?? '');
    $error = '';
    
    // Check if any items are selected
    if (!isset($_POST['quantity']) || empty($_POST['quantity'])) {
        $error = 'Vui lòng chọn ít nhất một món ăn';
    } else {
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Update order
            $update_order_sql = "UPDATE orders SET discount_percent = ?, notes = ? WHERE order_id = ?";
            $update_order_stmt = $conn->prepare($update_order_sql);
            $update_order_stmt->bind_param("dsi", $discount_percent, $notes, $order_id);
            $update_order_stmt->execute();
            
            // Delete all existing order details
            $delete_sql = "DELETE FROM order_details WHERE order_id = ?";
            $delete_stmt = $conn->prepare($delete_sql);
            $delete_stmt->bind_param("i", $order_id);
            $delete_stmt->execute();
            
            // Add new order details
            $quantities = $_POST['quantity'];
            $prices = $_POST['price'];
            
            foreach ($quantities as $food_id => $quantity) {
                if ($quantity > 0) {
                    $price = $prices[$food_id];
                    $subtotal = $quantity * $price;
                    
                    $detail_sql = "INSERT INTO order_details (order_id, food_id, quantity, unit_price, subtotal) 
                                  VALUES (?, ?, ?, ?, ?)";
                    $detail_stmt = $conn->prepare($detail_sql);
                    $detail_stmt->bind_param("iiidi", $order_id, $food_id, $quantity, $price, $subtotal);
                    $detail_stmt->execute();
                }
            }
            
            // Update order status to processing if it was pending
            if ($order['status'] == 'pending') {
                $status_sql = "UPDATE orders SET status = 'processing' WHERE order_id = ?";
                $status_stmt = $conn->prepare($status_sql);
                $status_stmt->bind_param("i", $order_id);
                $status_stmt->execute();
            }
            
            // Commit transaction
            $conn->commit();
            
            setFlashMessage('success', 'Đơn hàng đã được cập nhật thành công');
            redirect(STAFF_URL . '/edit_order.php?id=' . $order_id);
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $error = 'Đã xảy ra lỗi khi cập nhật đơn hàng: ' . $e->getMessage();
        }
    }
}

// Get all active food items grouped by category
$food_sql = "SELECT f.*, c.category_name FROM food_items f 
            JOIN categories c ON f.category_id = c.category_id 
            WHERE f.status = 1 AND c.status = 1
            ORDER BY c.category_name, f.food_name";
$food_result = $conn->query($food_sql);

// Group food items by category
$categories = [];
while ($food = $food_result->fetch_assoc()) {
    $category_name = $food['category_name'];
    if (!isset($categories[$category_name])) {
        $categories[$category_name] = [
            'category_id' => $food['category_id'],
            'items' => []
        ];
    }
    $categories[$category_name]['items'][] = $food;
}

// Get all categories for the filter
$category_sql = "SELECT category_id, category_name FROM categories WHERE status = 1 ORDER BY category_name";
$category_result = $conn->query($category_sql);

// Create array of existing order items for JavaScript
$existing_items = [];
while ($detail = $details_result->fetch_assoc()) {
    $existing_items[] = [
        'food_id' => $detail['food_id'],
        'food_name' => $detail['food_name'],
        'price' => $detail['unit_price'],
        'quantity' => $detail['quantity']
    ];
}
?>

<div class="row mb-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= STAFF_URL ?>">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="<?= STAFF_URL ?>/tables.php">Quản lý bàn</a></li>
                <li class="breadcrumb-item active" aria-current="page">Sửa đơn hàng #<?= $order_id ?></li>
            </ol>
        </nav>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <h2>Sửa đơn hàng #<?= $order_id ?></h2>
    </div>
    <div class="col-md-6 text-end">
        <h5>Bàn: <?= htmlspecialchars($order['table_number']) ?> (<?= $order['capacity'] ?> người)</h5>
        <p>Ngày tạo: <?= date('d/m/Y H:i', strtotime($order['order_date'])) ?></p>
    </div>
</div>

<?php if (isset($error) && !empty($error)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?= $error ?>
        <button type="button" class="btn-close" data-mdb-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<form method="post" action="">
    <div class="row">
        <!-- Food Items Selection -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Chọn món ăn</h5>
                </div>
                <div class="card-body">
                    <!-- Category Filter Buttons -->
                    <div class="mb-4">
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-primary active" onclick="filterFoodByCategory(0)">Tất cả</button>
                            <?php while ($category = $category_result->fetch_assoc()): ?>
                                <button type="button" class="btn btn-outline-primary" onclick="filterFoodByCategory(<?= $category['category_id'] ?>)">
                                    <?= htmlspecialchars($category['category_name']) ?>
                                </button>
                            <?php endwhile; ?>
                        </div>
                    </div>
                    
                    <!-- Food Items Grid -->
                    <div class="row">
                        <?php foreach ($categories as $category_name => $category): ?>
                            <?php foreach ($category['items'] as $food): ?>
                                <div class="col-md-4 mb-4 food-item-card" data-category="<?= $food['category_id'] ?>">
                                    <div class="card h-100">
                                        <?php if (!empty($food['image_path']) && file_exists('../' . $food['image_path'])): ?>
                                            <img src="<?= SITE_URL . '/' . $food['image_path'] ?>" class="card-img-top food-item-image" alt="<?= htmlspecialchars($food['food_name']) ?>">
                                        <?php else: ?>
                                            <div class="bg-light d-flex align-items-center justify-content-center food-item-image">
                                                <i class="fas fa-utensils fa-3x text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="card-body">
                                            <h5 class="card-title"><?= htmlspecialchars($food['food_name']) ?></h5>
                                            <p class="card-text text-primary fw-bold"><?= number_format($food['price'], 0, ',', '.') ?> đ</p>
                                            <button type="button" class="btn btn-primary btn-sm w-100" onclick="addItemToOrder(<?= $food['food_id'] ?>, '<?= addslashes($food['food_name']) ?>', <?= $food['price'] ?>)">
                                                <i class="fas fa-plus me-1"></i>Thêm
                                            </button>
                                        </div>
                                        
                                        <span class="badge bg-info category-badge"><?= htmlspecialchars($category_name) ?></span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Order Summary -->
        <div class="col-md-4">
            <div class="card position-sticky" style="top: 1rem;">
                <div class="card-header">
                    <h5 class="mb-0">Chi tiết đơn hàng</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="fw-bold mb-2">Món đã chọn:</div>
                        <div id="orderItems">
                            <!-- Order items will be added here by JavaScript -->
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="mb-3">
                        <div class="row mb-2">
                            <div class="col-6">Tổng tiền:</div>
                            <div class="col-6 text-end" id="subtotal">0 đ</div>
                            <input type="hidden" id="subtotalInput" name="subtotal" value="0">
                        </div>
                        
                        <div class="row mb-2">
                            <div class="col-6">
                                <label for="discountPercent" class="form-label">Giảm giá (%):</label>
                                <input type="number" class="form-control form-control-sm" id="discountPercent" name="discount_percent" min="0" max="100" value="<?= $order['discount_percent'] ?>">
                            </div>
                            <div class="col-6 text-end align-self-end" id="discountAmount">0 đ</div>
                            <input type="hidden" id="discountAmountInput" name="discount_amount" value="0">
                        </div>
                        
                        <div class="row fw-bold">
                            <div class="col-6">Thành tiền:</div>
                            <div class="col-6 text-end" id="finalAmount">0 đ</div>
                            <input type="hidden" id="finalAmountInput" name="final_amount" value="0">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Ghi chú:</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"><?= htmlspecialchars($order['notes']) ?></textarea>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Cập nhật đơn hàng
                        </button>
                        <a href="<?= STAFF_URL ?>/payment.php?id=<?= $order_id ?>" class="btn btn-success">
                            <i class="fas fa-cash-register me-2"></i>Thanh toán
                        </a>
                        <a href="<?= STAFF_URL ?>/tables.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Quay lại
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
// Load existing order items
document.addEventListener('DOMContentLoaded', function() {
    const existingItems = <?= json_encode($existing_items) ?>;
    
    existingItems.forEach(function(item) {
        addItemToOrder(item.food_id, item.food_name, item.price, item.quantity);
    });
});
</script>

<?php require_once '../includes/footer.php'; ?>