<?php
require_once '../includes/header.php';

// Check if user is admin
if (!isAdmin()) {
    setFlashMessage('danger', 'Bạn không có quyền truy cập trang này');
    redirect(SITE_URL);
}

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    setFlashMessage('danger', 'ID người dùng không hợp lệ');
    redirect(ADMIN_URL . '/users.php');
}

$user_id = (int)$_GET['id'];

// Get user data
$sql = "SELECT * FROM users WHERE user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    setFlashMessage('danger', 'Không tìm thấy người dùng');
    redirect(ADMIN_URL . '/users.php');
}

$user = $result->fetch_assoc();

// Get all roles
$role_sql = "SELECT role_id, role_name FROM roles ORDER BY role_id";
$role_result = $conn->query($role_sql);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = sanitize($_POST['email']);
    $full_name = sanitize($_POST['full_name']);
    $role_id = (int)$_POST['role_id'];
    $status = isset($_POST['status']) ? 1 : 0;
    $change_password = isset($_POST['change_password']) && $_POST['change_password'] == 1;
    $error = '';
    
    // Validate inputs
    if (empty($email)) {
        $error = 'Vui lòng nhập email';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Email không hợp lệ';
    } elseif (empty($full_name)) {
        $error = 'Vui lòng nhập họ và tên';
    } elseif ($role_id <= 0) {
        $error = 'Vui lòng chọn vai trò';
    } else {
        // Check if email already exists for other users
        $check_email_sql = "SELECT user_id FROM users WHERE email = ? AND user_id != ?";
        $check_email_stmt = $conn->prepare($check_email_sql);
        $check_email_stmt->bind_param("si", $email, $user_id);
        $check_email_stmt->execute();
        $check_email_result = $check_email_stmt->get_result();
        
        if ($check_email_result->num_rows > 0) {
            $error = 'Email đã tồn tại';
        } else {
            // Check for password change
            if ($change_password) {
                $password = $_POST['password'];
                $confirm_password = $_POST['confirm_password'];
                
                if (empty($password)) {
                    $error = 'Vui lòng nhập mật khẩu mới';
                } elseif (strlen($password) < 6) {
                    $error = 'Mật khẩu phải có ít nhất 6 ký tự';
                } elseif ($password != $confirm_password) {
                    $error = 'Mật khẩu xác nhận không khớp';
                }
            }
            
            if (empty($error)) {
                if ($change_password) {
                    // Hash the new password
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    
                    // Update user with new password
                    $sql = "UPDATE users SET email = ?, full_name = ?, role_id = ?, status = ?, password = ? WHERE user_id = ?";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("ssisii", $email, $full_name, $role_id, $status, $hashed_password, $user_id);
                } else {
                    // Update user without changing password
                    $sql = "UPDATE users SET email = ?, full_name = ?, role_id = ?, status = ? WHERE user_id = ?";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("ssiii", $email, $full_name, $role_id, $status, $user_id);
                }
                
                if ($stmt->execute()) {
                    setFlashMessage('success', 'Thông tin người dùng đã được cập nhật thành công');
                    redirect(ADMIN_URL . '/users.php');
                } else {
                    $error = 'Đã xảy ra lỗi khi cập nhật người dùng: ' . $conn->error;
                }
            }
        }
    }
}
?>

<div class="row mb-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= ADMIN_URL ?>">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="<?= ADMIN_URL ?>/users.php">Người dùng</a></li>
                <li class="breadcrumb-item active" aria-current="page">Sửa thông tin người dùng</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Sửa thông tin người dùng: <?= htmlspecialchars($user['username']) ?></h5>
            </div>
            <div class="card-body">
                <?php if (isset($error) && !empty($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?= $error ?>
                        <button type="button" class="btn-close" data-mdb-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <form method="post" action="">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-outline">
                                <input type="text" id="username" class="form-control" value="<?= htmlspecialchars($user['username']) ?>" readonly />
                                <label class="form-label" for="username">Tên đăng nhập</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-outline">
                                <input type="text" id="full_name" name="full_name" class="form-control" required value="<?= htmlspecialchars($_POST['full_name'] ?? $user['full_name']) ?>" />
                                <label class="form-label" for="full_name">Họ và tên</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-outline">
                            <input type="email" id="email" name="email" class="form-control" required value="<?= htmlspecialchars($_POST['email'] ?? $user['email']) ?>" />
                            <label class="form-label" for="email">Email</label>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="change_password" name="change_password" value="1" onchange="togglePasswordFields()" />
                            <label class="form-check-label" for="change_password">Thay đổi mật khẩu</label>
                        </div>
                    </div>
                    
                    <div id="password_fields" style="display: none;">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-outline">
                                    <input type="password" id="password" name="password" class="form-control" />
                                    <label class="form-label" for="password">Mật khẩu mới</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-outline">
                                    <input type="password" id="confirm_password" name="confirm_password" class="form-control" />
                                    <label class="form-label" for="confirm_password">Xác nhận mật khẩu</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <select class="form-select" name="role_id" required>
                                <option value="">Chọn vai trò</option>
                                <?php 
                                $role_result->data_seek(0); // Reset pointer
                                while ($role = $role_result->fetch_assoc()): 
                                    $selected = (isset($_POST['role_id']) && $_POST['role_id'] == $role['role_id']) || 
                                               (!isset($_POST['role_id']) && $user['role_id'] == $role['role_id']);
                                ?>
                                    <option value="<?= $role['role_id'] ?>" <?= $selected ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($role['role_name']) ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <?php 
                            $checked = (isset($_POST['status'])) || 
                                      (!isset($_POST['status']) && $user['status'] == 1);
                            ?>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="status" name="status" <?= $checked ? 'checked' : '' ?> />
                                <label class="form-check-label" for="status">Kích hoạt tài khoản</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">Cập nhật người dùng</button>
                        <a href="<?= ADMIN_URL ?>/users.php" class="btn btn-outline-secondary">Hủy bỏ</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function togglePasswordFields() {
    var changePassword = document.getElementById('change_password').checked;
    var passwordFields = document.getElementById('password_fields');
    
    if (changePassword) {
        passwordFields.style.display = 'block';
        document.getElementById('password').setAttribute('required', 'required');
        document.getElementById('confirm_password').setAttribute('required', 'required');
    } else {
        passwordFields.style.display = 'none';
        document.getElementById('password').removeAttribute('required');
        document.getElementById('confirm_password').removeAttribute('required');
    }
}
</script>

<?php require_once '../includes/footer.php'; ?>