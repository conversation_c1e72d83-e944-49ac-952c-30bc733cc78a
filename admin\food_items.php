<?php
require_once '../includes/header.php';

// Check if user is admin
if (!isAdmin()) {
    setFlashMessage('danger', 'Bạn không có quyền truy cập trang này');
    redirect(SITE_URL);
}

// Handle delete request
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $food_id = (int)$_GET['delete'];
    
    // First check if the food item exists
    $check_sql = "SELECT food_id, image_path, food_name FROM food_items WHERE food_id = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("i", $food_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows > 0) {
        $food_data = $check_result->fetch_assoc();
        
        // Check if this food item is used in orders
        $order_check_sql = "SELECT COUNT(*) as count FROM order_details WHERE food_id = ?";
        $order_check_stmt = $conn->prepare($order_check_sql);
        $order_check_stmt->bind_param("i", $food_id);
        $order_check_stmt->execute();
        $order_check_result = $order_check_stmt->get_result();
        $order_count = $order_check_result->fetch_assoc()['count'];
        
        if ($order_count > 0) {
            setFlashMessage('danger', 'Không thể xóa món ăn "' . $food_data['food_name'] . '" vì đã được sử dụng trong ' . $order_count . ' đơn hàng');
        } else {
            // Delete the food item
            $delete_sql = "DELETE FROM food_items WHERE food_id = ?";
            $delete_stmt = $conn->prepare($delete_sql);
            $delete_stmt->bind_param("i", $food_id);
            
            if ($delete_stmt->execute()) {
                // Delete the image file if it exists
                if (!empty($food_data['image_path']) && file_exists('../' . $food_data['image_path'])) {
                    unlink('../' . $food_data['image_path']);
                }
                
                setFlashMessage('success', 'Món ăn "' . $food_data['food_name'] . '" đã được xóa thành công');
            } else {
                setFlashMessage('danger', 'Không thể xóa món ăn: ' . $conn->error);
            }
        }
    } else {
        setFlashMessage('danger', 'Món ăn không tồn tại');
    }
    
    redirect(ADMIN_URL . '/food_items.php');
}

// Handle status toggle request via AJAX
if (isset($_POST['toggle_status']) && !empty($_POST['food_id'])) {
    $food_id = (int)$_POST['food_id'];
    $new_status = (int)$_POST['status'];
    
    $update_sql = "UPDATE food_items SET status = ? WHERE food_id = ?";
    $update_stmt = $conn->prepare($update_sql);
    $update_stmt->bind_param("ii", $new_status, $food_id);
    
    if ($update_stmt->execute()) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => $conn->error]);
    }
    exit;
}

// Get all food items with category name
$sql = "SELECT f.*, c.category_name, c.category_id 
        FROM food_items f 
        JOIN categories c ON f.category_id = c.category_id 
        ORDER BY f.food_name";
$result = $conn->query($sql);

// Get all categories for the filter and add form
$category_sql = "SELECT category_id, category_name FROM categories WHERE status = 1 ORDER BY category_name";
$category_result = $conn->query($category_sql);
$categories = [];
while ($category = $category_result->fetch_assoc()) {
    $categories[] = $category;
}

// Group food items by category for card view
$foods_by_category = [];
$result->data_seek(0);
while ($food = $result->fetch_assoc()) {
    $category_id = $food['category_id'];
    $category_name = $food['category_name'];
    
    if (!isset($foods_by_category[$category_id])) {
        $foods_by_category[$category_id] = [
            'name' => $category_name,
            'items' => []
        ];
    }
    
    $foods_by_category[$category_id]['items'][] = $food;
}
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= ADMIN_URL ?>">Trang chủ</a></li>
        <li class="breadcrumb-item active" aria-current="page">Quản lý món ăn</li>
    </ol>
</nav>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">
        <i class="material-icons text-primary me-2">restaurant_menu</i>
        Quản lý món ăn
    </h2>
    <div class="d-flex gap-2">
        <a href="add_food.php" class="btn btn-primary d-flex align-items-center">
            <i class="material-icons me-2">add_circle</i>Thêm món ăn mới
        </a>
        <div class="dropdown">
            <button class="btn btn-outline-primary dropdown-toggle" type="button" id="viewToggleDropdown" data-mdb-toggle="dropdown" aria-expanded="false">
                <i class="material-icons me-1">view_module</i>
                Chế độ xem
            </button>
            <ul class="dropdown-menu shadow-sm" aria-labelledby="viewToggleDropdown">
                <li><a class="dropdown-item d-flex align-items-center view-toggle active" href="#" data-view="cards">
                    <i class="material-icons me-2">grid_view</i>Thẻ món ăn
                </a></li>
                <li><a class="dropdown-item d-flex align-items-center view-toggle" href="#" data-view="table">
                    <i class="material-icons me-2">view_list</i>Bảng danh sách
                </a></li>
            </ul>
        </div>
    </div>
</div>

<!-- Filter Section -->
<div class="card mb-4 shadow-sm">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-lg-4 col-md-6">
                <div class="input-group">
                    <span class="input-group-text bg-transparent border-end-0">
                        <i class="material-icons text-primary">search</i>
                    </span>
                    <input type="text" id="searchInput" class="form-control border-start-0" placeholder="Tìm kiếm món ăn..." aria-label="Tìm kiếm món ăn">
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <select id="categoryFilter" class="form-select">
                    <option value="0">Tất cả danh mục</option>
                    <?php foreach ($categories as $category): ?>
                    <option value="<?= $category['category_id'] ?>"><?= htmlspecialchars($category['category_name']) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-lg-3 col-md-6">
                <select id="statusFilter" class="form-select">
                    <option value="all">Tất cả trạng thái</option>
                    <option value="1">Có sẵn</option>
                    <option value="0">Không có sẵn</option>
                </select>
            </div>
            <div class="col-lg-2 col-md-6 d-flex">
                <button id="applyFilters" class="btn btn-primary flex-grow-1">
                    <i class="material-icons me-2">filter_list</i>Lọc
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Food Items Stats -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card dashboard-card bg-white">
            <div class="card-body">
                <div class="icon-bg bg-primary bg-opacity-10">
                    <i class="material-icons">restaurant_menu</i>
                </div>
                <h5 class="card-title">Tổng số món ăn</h5>
                <?php 
                $result->data_seek(0);
                $total_count = $result->num_rows;
                ?>
                <h3 class="text-primary"><?= $total_count ?></h3>
                <p class="card-text text-muted small">Trên toàn hệ thống</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card dashboard-card bg-white">
            <div class="card-body">
                <div class="icon-bg bg-success bg-opacity-10">
                    <i class="material-icons">check_circle</i>
                </div>
                <h5 class="card-title">Món có sẵn</h5>
                <?php 
                $available_count = 0;
                $result->data_seek(0);
                while ($food = $result->fetch_assoc()) {
                    if ($food['status'] == 1) {
                        $available_count++;
                    }
                }
                ?>
                <h3 class="text-success"><?= $available_count ?></h3>
                <p class="card-text text-muted small">Sẵn sàng phục vụ</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card dashboard-card bg-white">
            <div class="card-body">
                <div class="icon-bg bg-danger bg-opacity-10">
                    <i class="material-icons">cancel</i>
                </div>
                <h5 class="card-title">Món không có sẵn</h5>
                <?php 
                $unavailable_count = $total_count - $available_count;
                ?>
                <h3 class="text-danger"><?= $unavailable_count ?></h3>
                <p class="card-text text-muted small">Tạm ngừng phục vụ</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card dashboard-card bg-white">
            <div class="card-body">
                <div class="icon-bg bg-info bg-opacity-10">
                    <i class="material-icons">category</i>
                </div>
                <h5 class="card-title">Số danh mục</h5>
                <h3 class="text-info"><?= count($foods_by_category) ?></h3>
                <p class="card-text text-muted small">Phân loại món ăn</p>
            </div>
        </div>
    </div>
</div>

<!-- Food Items Card View -->
<div id="cardsView" class="view-content active">
    <?php if ($result->num_rows > 0): ?>
        <?php foreach ($foods_by_category as $category_id => $category): ?>
            <div class="category-section mb-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h3 class="m-0 text-primary d-flex align-items-center">
                        <i class="material-icons me-2">category</i>
                        <?= htmlspecialchars($category['name']) ?>
                    </h3>
                    <span class="badge bg-primary rounded-pill"><?= count($category['items']) ?> món</span>
                </div>
                
                <div class="row">
                    <?php foreach ($category['items'] as $food): ?>
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4 food-item" 
                             data-category="<?= $food['category_id'] ?>" 
                             data-status="<?= $food['status'] ?>"
                             data-name="<?= htmlspecialchars($food['food_name']) ?>">
                            <div class="card h-100 food-item-card">
                                <?php if (!empty($food['image_path']) && file_exists('../' . $food['image_path'])): ?>
                                    <img src="<?= SITE_URL . '/' . $food['image_path'] ?>" class="card-img-top food-item-image" alt="<?= htmlspecialchars($food['food_name']) ?>" />
                                <?php else: ?>
                                    <div class="bg-light d-flex align-items-center justify-content-center food-item-image">
                                        <i class="material-icons" style="font-size: 48px; color: #ccc;">restaurant</i>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="card-body d-flex flex-column">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h5 class="card-title mb-0 text-truncate-2"><?= htmlspecialchars($food['food_name']) ?></h5>
                                        <div class="form-check form-switch ms-2">
                                            <input class="form-check-input status-toggle" type="checkbox" 
                                                   id="statusToggle<?= $food['food_id'] ?>" 
                                                   data-food-id="<?= $food['food_id'] ?>" 
                                                   <?= $food['status'] == 1 ? 'checked' : '' ?> />
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3 text-truncate-2" style="height: 48px; overflow: hidden;">
                                        <?php if (!empty($food['description'])): ?>
                                            <?= htmlspecialchars($food['description']) ?>
                                        <?php else: ?>
                                            <span class="text-muted fst-italic">Không có mô tả</span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between align-items-center mt-auto">
                                        <span class="fw-bold text-primary"><?= number_format($food['price'], 0, ',', '.') ?> đ</span>
                                        <div>
                                            <?php if ($food['status'] == 1): ?>
                                                <span class="badge bg-success bg-opacity-10 text-success px-2 py-1 rounded-pill">
                                                    <i class="material-icons fs-6 me-1">check_circle</i>
                                                    Có sẵn
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-danger bg-opacity-10 text-danger px-2 py-1 rounded-pill">
                                                    <i class="material-icons fs-6 me-1">cancel</i>
                                                    Không có sẵn
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="card-footer bg-transparent">
                                    <div class="btn-group w-100">
                                        <a href="edit_food.php?id=<?= $food['food_id'] ?>" class="btn btn-outline-primary" data-mdb-toggle="tooltip" title="Sửa món ăn">
                                            <i class="material-icons">edit</i>
                                        </a>
                                        <a href="food_items.php?delete=<?= $food['food_id'] ?>" class="btn btn-outline-danger btn-delete" data-mdb-toggle="tooltip" title="Xóa món ăn">
                                            <i class="material-icons">delete</i>
                                        </a>
                                        <button type="button" class="btn btn-outline-info view-details" data-food-id="<?= $food['food_id'] ?>" data-mdb-toggle="tooltip" title="Xem chi tiết">
                                            <i class="material-icons">visibility</i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="text-center py-5">
            <i class="material-icons" style="font-size: 4rem; color: #ccc;">restaurant_menu</i>
            <p class="mt-3 text-muted">Chưa có món ăn nào trong hệ thống</p>
            <a href="add_food.php" class="btn btn-primary d-inline-flex align-items-center">
                <i class="material-icons me-2">add_circle</i>
                Thêm món ăn đầu tiên
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Food Items Table View -->
<div id="tableView" class="view-content">
    <div class="card shadow-sm">
        <div class="card-body">
            <?php if ($result->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover align-middle" id="foodItemsTable">
                        <thead class="table-light">
                            <tr>
                                <th scope="col" style="width: 60px;">ID</th>
                                <th scope="col" style="width: 80px;">Hình ảnh</th>
                                <th scope="col">Tên món</th>
                                <th scope="col">Danh mục</th>
                                <th scope="col" style="width: 120px;">Giá</th>
                                <th scope="col" style="width: 100px;">Trạng thái</th>
                                <th scope="col" style="width: 150px;">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $result->data_seek(0);
                            while ($food = $result->fetch_assoc()): 
                            ?>
                                <tr class="food-item" data-category="<?= $food['category_id'] ?>" data-status="<?= $food['status'] ?>" data-name="<?= htmlspecialchars($food['food_name']) ?>">
                                    <td><?= $food['food_id'] ?></td>
                                    <td>
                                        <?php if (!empty($food['image_path']) && file_exists('../' . $food['image_path'])): ?>
                                            <img src="<?= SITE_URL . '/' . $food['image_path'] ?>" alt="<?= htmlspecialchars($food['food_name']) ?>" width="60" height="60" class="rounded shadow-sm">
                                        <?php else: ?>
                                            <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                                <i class="material-icons text-muted">restaurant</i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="fw-bold"><?= htmlspecialchars($food['food_name']) ?></div>
                                        <div class="text-muted small text-truncate" style="max-width: 200px;">
                                            <?= htmlspecialchars($food['description'] ?: 'Không có mô tả') ?>
                                        </div>
                                    </td>
                                    <td><?= htmlspecialchars($food['category_name']) ?></td>
                                    <td class="fw-bold text-primary"><?= number_format($food['price'], 0, ',', '.') ?> đ</td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input status-toggle" type="checkbox" 
                                                   id="tableStatusToggle<?= $food['food_id'] ?>" 
                                                   data-food-id="<?= $food['food_id'] ?>" 
                                                   <?= $food['status'] == 1 ? 'checked' : '' ?> />
                                            <label class="form-check-label" for="tableStatusToggle<?= $food['food_id'] ?>">
                                                <?= $food['status'] == 1 ? 'Có sẵn' : 'Không có sẵn' ?>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <a href="edit_food.php?id=<?= $food['food_id'] ?>" class="btn btn-primary btn-floating btn-sm" data-mdb-toggle="tooltip" title="Sửa món ăn">
                                                <i class="material-icons">edit</i>
                                            </a>
                                            <a href="food_items.php?delete=<?= $food['food_id'] ?>" class="btn btn-danger btn-floating btn-sm btn-delete" data-mdb-toggle="tooltip" title="Xóa món ăn">
                                                <i class="material-icons">delete</i>
                                            </a>
                                            <button type="button" class="btn btn-info btn-floating btn-sm view-details" data-food-id="<?= $food['food_id'] ?>" data-mdb-toggle="tooltip" title="Xem chi tiết">
                                                <i class="material-icons">visibility</i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="material-icons" style="font-size: 4rem; color: #ccc;">restaurant_menu</i>
                    <p class="mt-3 text-muted">Chưa có món ăn nào trong hệ thống</p>
                    <a href="add_food.php" class="btn btn-primary">
                        <i class="material-icons me-2">add_circle</i>
                        Thêm món ăn đầu tiên
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Food Detail Modal -->
<div class="modal fade" id="foodDetailModal" tabindex="-1" aria-labelledby="foodDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="foodDetailModalLabel">Chi tiết món ăn</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-5">
                        <div id="modalFoodImage" class="rounded mb-3" style="height: 250px; background-position: center; background-size: cover; background-repeat: no-repeat;"></div>
                    </div>
                    <div class="col-md-7">
                        <h4 id="modalFoodName" class="mb-3"></h4>
                        <p><strong>Danh mục:</strong> <span id="modalFoodCategory"></span></p>
                        <p><strong>Giá:</strong> <span id="modalFoodPrice" class="text-primary fw-bold"></span></p>
                        <p><strong>Trạng thái:</strong> <span id="modalFoodStatus"></span></p>
                        <hr>
                        <h6>Mô tả:</h6>
                        <p id="modalFoodDescription"></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a id="modalEditLink" href="#" class="btn btn-primary">
                    <i class="material-icons me-2">edit</i>Sửa
                </a>
                <button type="button" class="btn btn-secondary" data-mdb-dismiss="modal">
                    <i class="material-icons me-2">close</i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize view toggle
    const viewToggles = document.querySelectorAll('.view-toggle');
    const viewContents = document.querySelectorAll('.view-content');
    
    viewToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all toggles
            viewToggles.forEach(t => t.classList.remove('active'));
            
            // Add active class to clicked toggle
            this.classList.add('active');
            
            // Hide all views
            viewContents.forEach(content => content.classList.remove('active'));
            
            // Show selected view
            const viewToShow = this.getAttribute('data-view');
            document.getElementById(viewToShow + 'View').classList.add('active');
            
            // Save preference to localStorage
            localStorage.setItem('foodItemsViewPreference', viewToShow);
        });
    });
    
    // Load saved view preference
    const savedView = localStorage.getItem('foodItemsViewPreference');
    if (savedView) {
        const savedToggle = document.querySelector(`.view-toggle[data-view="${savedView}"]`);
        if (savedToggle) {
            savedToggle.click();
        }
    }
    
    // Initialize filters
    const searchInput = document.getElementById('searchInput');
    const categoryFilter = document.getElementById('categoryFilter');
    const statusFilter = document.getElementById('statusFilter');
    const applyFiltersBtn = document.getElementById('applyFilters');
    
    function filterItems() {
        const searchTerm = searchInput.value.toLowerCase();
        const categoryValue = categoryFilter.value;
        const statusValue = statusFilter.value;
        
        document.querySelectorAll('.food-item').forEach(item => {
            const name = item.getAttribute('data-name').toLowerCase();
            const category = item.getAttribute('data-category');
            const status = item.getAttribute('data-status');
            
            const nameMatch = name.includes(searchTerm);
            const categoryMatch = categoryValue === "0" || category === categoryValue;
            const statusMatch = statusValue === "all" || status === statusValue;
            
            if (nameMatch && categoryMatch && statusMatch) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
        
        // Hide empty category sections in card view
        document.querySelectorAll('.category-section').forEach(section => {
            const visibleItems = section.querySelectorAll('.food-item[style=""]').length;
            if (visibleItems === 0) {
                section.style.display = 'none';
            } else {
                section.style.display = '';
            }
        });
    }
    
    // Apply filters on button click
    applyFiltersBtn.addEventListener('click', filterItems);
    
    // Apply filters on enter key in search box
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            filterItems();
        }
    });
    
    // Initialize status toggles
    document.querySelectorAll('.status-toggle').forEach(toggle => {
        toggle.addEventListener('change', function() {
            const foodId = this.getAttribute('data-food-id');
            const isChecked = this.checked;
            const newStatus = isChecked ? 1 : 0;
            
            // Show loading overlay
            showLoading();
            
            // Send AJAX request to toggle status
            fetch('food_items.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `toggle_status=1&food_id=${foodId}&status=${newStatus}`
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                
                if (data.success) {
                    // Update all instances of this toggle (in both views)
                    document.querySelectorAll(`.status-toggle[data-food-id="${foodId}"]`).forEach(t => {
                        t.checked = isChecked;
                    });
                    
                    // Update status badges and data attributes
                    document.querySelectorAll(`.food-item[data-food-id="${foodId}"]`).forEach(item => {
                        item.setAttribute('data-status', newStatus);
                        const statusBadge = item.querySelector('.badge');
                        if (statusBadge) {
                            if (isChecked) {
                                statusBadge.className = 'badge bg-success bg-opacity-10 text-success px-2 py-1 rounded-pill';
                                statusBadge.innerHTML = '<i class="material-icons fs-6 me-1">check_circle</i>Có sẵn';
                            } else {
                                statusBadge.className = 'badge bg-danger bg-opacity-10 text-danger px-2 py-1 rounded-pill';
                                statusBadge.innerHTML = '<i class="material-icons fs-6 me-1">cancel</i>Không có sẵn';
                            }
                        }
                    });
                    
                    // Update labels in table view
                    const tableLabel = document.querySelector(`label[for="tableStatusToggle${foodId}"]`);
                    if (tableLabel) {
                        tableLabel.textContent = isChecked ? 'Có sẵn' : 'Không có sẵn';
                    }
                    
                    showToast(isChecked ? 'Món ăn đã được kích hoạt' : 'Món ăn đã bị vô hiệu hóa', isChecked ? 'success' : 'warning');
                } else {
                    showToast('Không thể cập nhật trạng thái món ăn', 'danger');
                    
                    // Revert toggle state
                    this.checked = !isChecked;
                }
            })
            .catch(error => {
                hideLoading();
                showToast('Đã xảy ra lỗi khi cập nhật trạng thái', 'danger');
                console.error('Error:', error);
                
                // Revert toggle state
                this.checked = !isChecked;
            });
        });
    });
    
    // Initialize view details buttons
    document.querySelectorAll('.view-details').forEach(btn => {
        btn.addEventListener('click', function() {
            const foodId = this.getAttribute('data-food-id');
            const foodItem = document.querySelector(`.food-item[data-food-id="${foodId}"]`);
            
            if (foodItem) {
                const modal = new mdb.Modal(document.getElementById('foodDetailModal'));
                
                // Get food details
                const foodName = foodItem.querySelector('.card-title, .fw-bold').textContent;
                const foodCategory = foodItem.closest('.category-section')?.querySelector('h3')?.textContent.trim() || 
                                     foodItem.querySelector('td:nth-child(4)')?.textContent.trim();
                const foodPrice = foodItem.querySelector('.text-primary').textContent;
                const foodStatus = foodItem.querySelector('.badge').textContent.trim();
                const foodDescription = foodItem.querySelector('.text-truncate-2, .text-muted.small')?.textContent.trim() || 'Không có mô tả';
                const foodImage = foodItem.querySelector('.food-item-image, img')?.src || '';
                
                // Populate modal
                document.getElementById('modalFoodName').textContent = foodName;
                document.getElementById('modalFoodCategory').textContent = foodCategory;
                document.getElementById('modalFoodPrice').textContent = foodPrice;
                document.getElementById('modalFoodStatus').textContent = foodStatus;
                document.getElementById('modalFoodDescription').textContent = foodDescription;
                
                if (foodImage) {
                    document.getElementById('modalFoodImage').style.backgroundImage = `url('${foodImage}')`;
                } else {
                    document.getElementById('modalFoodImage').style.backgroundImage = '';
                    document.getElementById('modalFoodImage').innerHTML = '<div class="h-100 d-flex align-items-center justify-content-center bg-light"><i class="material-icons" style="font-size: 48px; color: #ccc;">restaurant</i></div>';
                }
                
                // Set edit link
                document.getElementById('modalEditLink').href = `edit_food.php?id=${foodId}`;
                
                // Show modal
                modal.show();
            }
        });
    });
    
    // Initialize delete confirmations
    document.querySelectorAll('.btn-delete').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const foodName = this.closest('.food-item-card')?.querySelector('.card-title')?.textContent || 
                            this.closest('tr')?.querySelector('.fw-bold')?.textContent || 'món ăn này';
            
            if (confirm(`Bạn có chắc chắn muốn xóa "${foodName}"?`)) {
                window.location.href = this.getAttribute('href');
            }
        });
    });
});

// CSS for view toggle
document.addEventListener('DOMContentLoaded', function() {
    const style = document.createElement('style');
    style.textContent = `
        .view-content {
            display: none;
        }
        .view-content.active {
            display: block;
        }
        .view-toggle.active {
            background-color: rgba(13, 110, 253, 0.1);
            color: #0d6efd;
        }
    `;
    document.head.appendChild(style);
});
</script>

<?php require_once '../includes/footer.php'; ?>