<?php
require_once '../includes/header.php';

// Check if user is staff
if (!isStaff()) {
    setFlashMessage('danger', 'Bạn không có quyền truy cập trang này');
    redirect(SITE_URL);
}

// Get selected date, default to today
$selected_date = isset($_GET['date']) ? sanitize($_GET['date']) : date('Y-m-d');

// Get staff ID
$staff_id = $_SESSION['user_id'];

// Get daily revenue for the staff
$revenue_sql = "SELECT 
                    SUM(p.payment_amount) as total_revenue,
                    COUNT(DISTINCT p.order_id) as total_orders
                FROM 
                    payments p
                JOIN 
                    orders o ON p.order_id = o.order_id
                WHERE 
                    DATE(p.payment_date) = ? 
                    AND p.payment_status = 'completed'
                    AND p.received_by = ?";
$revenue_stmt = $conn->prepare($revenue_sql);
$revenue_stmt->bind_param("si", $selected_date, $staff_id);
$revenue_stmt->execute();
$revenue_result = $revenue_stmt->get_result();
$revenue = $revenue_result->fetch_assoc();

// Get all orders processed by the staff on the selected date
$orders_sql = "SELECT 
                o.order_id, o.order_date, t.table_number, o.final_amount,
                p.payment_date, p.payment_method
              FROM 
                payments p
              JOIN 
                orders o ON p.order_id = o.order_id
              JOIN
                tables t ON o.table_id = t.table_id
              WHERE 
                DATE(p.payment_date) = ? 
                AND p.payment_status = 'completed'
                AND p.received_by = ?
              ORDER BY 
                p.payment_date DESC";
$orders_stmt = $conn->prepare($orders_sql);
$orders_stmt->bind_param("si", $selected_date, $staff_id);
$orders_stmt->execute();
$orders_result = $orders_stmt->get_result();

// Get payment method statistics
$payment_methods_sql = "SELECT 
                          p.payment_method,
                          COUNT(*) as count,
                          SUM(p.payment_amount) as total
                        FROM 
                          payments p
                        WHERE 
                          DATE(p.payment_date) = ? 
                          AND p.payment_status = 'completed'
                          AND p.received_by = ?
                        GROUP BY 
                          p.payment_method";
$payment_methods_stmt = $conn->prepare($payment_methods_sql);
$payment_methods_stmt->bind_param("si", $selected_date, $staff_id);
$payment_methods_stmt->execute();
$payment_methods_result = $payment_methods_stmt->get_result();
$payment_methods = [];

while ($method = $payment_methods_result->fetch_assoc()) {
    $payment_methods[$method['payment_method']] = $method;
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h2>Doanh thu trong ngày</h2>
    </div>
    <div class="col-md-6">
        <form method="get" action="" class="d-flex justify-content-end">
            <div class="input-group w-auto">
                <input type="date" class="form-control" name="date" value="<?= $selected_date ?>" max="<?= date('Y-m-d') ?>">
                <button class="btn btn-primary" type="submit">Xem</button>
            </div>
        </form>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-body text-center">
                <div class="display-6 text-primary mb-3">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <h5 class="card-title">Tổng doanh thu</h5>
                <h2 class="text-primary"><?= number_format($revenue['total_revenue'] ?? 0, 0, ',', '.') ?> đ</h2>
                <p class="text-muted">Ngày <?= date('d/m/Y', strtotime($selected_date)) ?></p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-body text-center">
                <div class="display-6 text-success mb-3">
                    <i class="fas fa-receipt"></i>
                </div>
                <h5 class="card-title">Số đơn hàng</h5>
                <h2 class="text-success"><?= $revenue['total_orders'] ?? 0 ?></h2>
                <p class="text-muted">Đơn hàng đã thanh toán</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-body text-center">
                <div class="display-6 text-info mb-3">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h5 class="card-title">Trung bình mỗi đơn</h5>
                <h2 class="text-info">
                    <?php
                    if (($revenue['total_orders'] ?? 0) > 0) {
                        echo number_format($revenue['total_revenue'] / $revenue['total_orders'], 0, ',', '.');
                    } else {
                        echo '0';
                    }
                    ?> đ
                </h2>
                <p class="text-muted">Giá trị trung bình</p>
            </div>
        </div>
    </div>
</div>

<!-- Payment Methods Card -->
<div class="row mb-4">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Phương thức thanh toán</h5>
            </div>
            <div class="card-body">
                <?php if (!empty($payment_methods)): ?>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Phương thức</th>
                                    <th>Số đơn</th>
                                    <th>Tổng tiền</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($payment_methods as $method_name => $method): ?>
                                    <tr>
                                        <td>
                                            <?php
                                            switch ($method_name) {
                                                case 'cash':
                                                    echo '<i class="fas fa-money-bill text-success me-2"></i>Tiền mặt';
                                                    break;
                                                case 'credit_card':
                                                    echo '<i class="fas fa-credit-card text-primary me-2"></i>Thẻ tín dụng';
                                                    break;
                                                case 'debit_card':
                                                    echo '<i class="fas fa-credit-card text-info me-2"></i>Thẻ ghi nợ';
                                                    break;
                                                case 'mobile_payment':
                                                    echo '<i class="fas fa-mobile-alt text-warning me-2"></i>Thanh toán di động';
                                                    break;
                                                default:
                                                    echo $method_name;
                                            }
                                            ?>
                                        </td>
                                        <td><?= $method['count'] ?></td>
                                        <td><?= number_format($method['total'], 0, ',', '.') ?> đ</td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-center">Không có dữ liệu thanh toán trong ngày này</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Biểu đồ doanh thu theo giờ</h5>
            </div>
            <div class="card-body">
                <?php if (($revenue['total_orders'] ?? 0) > 0): ?>
                    <canvas id="hourlyRevenueChart" height="250"></canvas>
                <?php else: ?>
                    <p class="text-center">Không có dữ liệu doanh thu trong ngày này</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Orders Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Danh sách đơn hàng đã thanh toán</h5>
    </div>
    <div class="card-body">
        <?php if ($orders_result->num_rows > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Bàn</th>
                            <th>Thời gian tạo</th>
                            <th>Thời gian thanh toán</th>
                            <th>Phương thức</th>
                            <th>Tổng tiền</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($order = $orders_result->fetch_assoc()): ?>
                            <tr>
                                <td><?= $order['order_id'] ?></td>
                                <td><?= htmlspecialchars($order['table_number']) ?></td>
                                <td><?= date('H:i:s', strtotime($order['order_date'])) ?></td>
                                <td><?= date('H:i:s', strtotime($order['payment_date'])) ?></td>
                                <td>
                                    <?php
                                    switch ($order['payment_method']) {
                                        case 'cash':
                                            echo '<span class="badge bg-success">Tiền mặt</span>';
                                            break;
                                        case 'credit_card':
                                            echo '<span class="badge bg-primary">Thẻ tín dụng</span>';
                                            break;
                                        case 'debit_card':
                                            echo '<span class="badge bg-info">Thẻ ghi nợ</span>';
                                            break;
                                        case 'mobile_payment':
                                            echo '<span class="badge bg-warning">Thanh toán di động</span>';
                                            break;
                                    }
                                    ?>
                                </td>
                                <td><?= number_format($order['final_amount'], 0, ',', '.') ?> đ</td>
                                <td>
                                    <a href="<?= STAFF_URL ?>/view_order.php?id=<?= $order['order_id'] ?>" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <p class="text-center">Không có đơn hàng nào trong ngày này</p>
        <?php endif; ?>
    </div>
</div>

<?php if (($revenue['total_orders'] ?? 0) > 0): ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get hourly revenue data
    <?php
    $hourly_sql = "SELECT 
                      HOUR(p.payment_date) as hour,
                      SUM(p.payment_amount) as total
                    FROM 
                      payments p
                    WHERE 
                      DATE(p.payment_date) = ? 
                      AND p.payment_status = 'completed'
                      AND p.received_by = ?
                    GROUP BY 
                      HOUR(p.payment_date)
                    ORDER BY 
                      hour";
    $hourly_stmt = $conn->prepare($hourly_sql);
    $hourly_stmt->bind_param("si", $selected_date, $staff_id);
    $hourly_stmt->execute();
    $hourly_result = $hourly_stmt->get_result();
    
    $hours = [];
    $totals = [];
    
    while ($row = $hourly_result->fetch_assoc()) {
        $hours[] = $row['hour'] . ':00';
        $totals[] = $row['total'];
    }
    ?>
    
    // Hourly Revenue Chart
    var ctx = document.getElementById('hourlyRevenueChart').getContext('2d');
    var hourlyRevenueChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: <?= json_encode($hours) ?>,
            datasets: [{
                label: 'Doanh thu',
                data: <?= json_encode($totals) ?>,
                backgroundColor: 'rgba(13, 110, 253, 0.7)',
                borderColor: 'rgba(13, 110, 253, 1)',
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString('vi-VN') + ' đ';
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': ' + context.raw.toLocaleString('vi-VN') + ' đ';
                        }
                    }
                }
            }
        }
    });
});
</script>
<?php endif; ?>

<?php require_once '../includes/footer.php'; ?>