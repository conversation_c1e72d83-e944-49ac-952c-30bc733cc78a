<?php
// Database configuration
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'restaurant_management');

// Attempt to connect to MySQL database
$conn = new mysqli(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);

// Check connection
if ($conn->connect_error) {
    die("Kết nối thất bại: " . $conn->connect_error);
}

// Set character set to utf8
$conn->set_charset("utf8mb4");

// Set default timezone
date_default_timezone_set('Asia/Ho_Chi_Minh');

// Session configuration
session_start();

// Define application constants
define('SITE_URL', 'http://do-an.test:8189');
define('ADMIN_URL', SITE_URL . '/admin');
define('STAFF_URL', SITE_URL . '/staff');
define('ASSETS_URL', SITE_URL . '/assets');

// Define user roles
define('ROLE_ADMIN', 1);
define('ROLE_STAFF', 2);

// Function to check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// Function to check if user is admin
function isAdmin() {
    return isset($_SESSION['role_id']) && $_SESSION['role_id'] == ROLE_ADMIN;
}

// Function to check if user is staff
function isStaff() {
    return isset($_SESSION['role_id']) && $_SESSION['role_id'] == ROLE_STAFF;
}

// Function to redirect to a specific page
function redirect($url) {
    header("Location: $url");
    exit;
}

// Function to sanitize input data
function sanitize($data) {
    global $conn;
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $conn->real_escape_string($data);
}

// Function to display flash messages
function setFlashMessage($type, $message) {
    $_SESSION['flash_message'] = [
        'type' => $type,
        'message' => $message
    ];
}

function displayFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $type = $_SESSION['flash_message']['type'];
        $message = $_SESSION['flash_message']['message'];
        echo "<div class='alert alert-$type alert-dismissible fade show' role='alert'>
                $message
                <button type='button' class='btn-close' data-mdb-dismiss='alert' aria-label='Close'></button>
              </div>";
        unset($_SESSION['flash_message']);
    }
}

// Function to generate random token
function generateToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

// Function to detect mobile devices
function isMobileDevice() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $mobileKeywords = [
        'Android', 'iPhone', 'iPad', 'iPod', 'BlackBerry', 'Windows Phone', 
        'webOS', 'Opera Mini', 'IEMobile', 'Mobile', 'Tablet'
    ];
    
    foreach ($mobileKeywords as $keyword) {
        if (stripos($userAgent, $keyword) !== false) {
            return true;
        }
    }
    
    return false;
}

// Function to add activity log
function logActivity($user_id, $activity_type, $description) {
    global $conn;
    
    $stmt = $conn->prepare("INSERT INTO activity_log (user_id, activity_type, description) VALUES (?, ?, ?)");
    $stmt->bind_param("iss", $user_id, $activity_type, $description);
    return $stmt->execute();
}

// Function to add notification
function addNotification($user_id, $message, $type = 'system') {
    global $conn;
    
    $stmt = $conn->prepare("INSERT INTO notifications (user_id, message, type) VALUES (?, ?, ?)");
    $stmt->bind_param("iss", $user_id, $message, $type);
    return $stmt->execute();
}
?>