<?php
require_once '../includes/header.php';

// Check if user is staff
if (!isStaff()) {
    setFlashMessage('danger', 'Bạn không có quyền truy cập trang này');
    redirect(SITE_URL);
}

// Check if order ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    setFlashMessage('danger', 'ID đơn hàng không hợp lệ');
    redirect(STAFF_URL . '/tables.php');
}

$order_id = (int)$_GET['id'];

// Get order information
$order_sql = "SELECT o.*, t.table_number, u.username as created_by_username
              FROM orders o
              JOIN tables t ON o.table_id = t.table_id
              JOIN users u ON o.user_id = u.user_id
              WHERE o.order_id = ?";
$order_stmt = $conn->prepare($order_sql);
$order_stmt->bind_param("i", $order_id);
$order_stmt->execute();
$order_result = $order_stmt->get_result();

if ($order_result->num_rows == 0) {
    setFlashMessage('danger', 'Không tìm thấy đơn hàng');
    redirect(STAFF_URL . '/tables.php');
}

$order = $order_result->fetch_assoc();

// Get order details
$details_sql = "SELECT od.*, f.food_name 
                FROM order_details od
                JOIN food_items f ON od.food_id = f.food_id
                WHERE od.order_id = ?";
$details_stmt = $conn->prepare($details_sql);
$details_stmt->bind_param("i", $order_id);
$details_stmt->execute();
$details_result = $details_stmt->get_result();

// Get payment information
$payment_sql = "SELECT p.*, u.username as received_by_username
                FROM payments p
                JOIN users u ON p.received_by = u.user_id
                WHERE p.order_id = ?";
$payment_stmt = $conn->prepare($payment_sql);
$payment_stmt->bind_param("i", $order_id);
$payment_stmt->execute();
$payment_result = $payment_stmt->get_result();
$payment = $payment_result->num_rows > 0 ? $payment_result->fetch_assoc() : null;

// Get table transfer history
$transfer_sql = "SELECT tt.*, 
                  t1.table_number as from_table_number,
                  t2.table_number as to_table_number,
                  u.username as transferred_by_username
                FROM table_transfers tt
                JOIN tables t1 ON tt.from_table_id = t1.table_id
                JOIN tables t2 ON tt.to_table_id = t2.table_id
                JOIN users u ON tt.transferred_by = u.user_id
                WHERE tt.order_id = ?
                ORDER BY tt.transfer_date DESC";
$transfer_stmt = $conn->prepare($transfer_sql);
$transfer_stmt->bind_param("i", $order_id);
$transfer_stmt->execute();
$transfer_result = $transfer_stmt->get_result();
?>

<div class="row mb-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= STAFF_URL ?>">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="<?= STAFF_URL ?>/tables.php">Quản lý bàn</a></li>
                <li class="breadcrumb-item active" aria-current="page">Chi tiết đơn hàng #<?= $order_id ?></li>
            </ol>
        </nav>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <h2>Chi tiết đơn hàng #<?= $order_id ?></h2>
    </div>
    <div class="col-md-6 text-end">
        <div class="btn-group">
            <?php if ($order['status'] == 'pending' || $order['status'] == 'processing'): ?>
                <a href="<?= STAFF_URL ?>/edit_order.php?id=<?= $order_id ?>" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>Sửa đơn hàng
                </a>
                <a href="<?= STAFF_URL ?>/payment.php?id=<?= $order_id ?>" class="btn btn-success">
                    <i class="fas fa-cash-register me-2"></i>Thanh toán
                </a>
            <?php else: ?>
                <a href="#" class="btn btn-secondary" onclick="window.print()">
                    <i class="fas fa-print me-2"></i>In hóa đơn
                </a>
            <?php endif; ?>
            <a href="<?= STAFF_URL ?>/tables.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Quay lại
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Order Details -->
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Thông tin đơn hàng</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>Bàn:</strong> <?= htmlspecialchars($order['table_number']) ?></p>
                        <p><strong>Ngày tạo:</strong> <?= date('d/m/Y H:i', strtotime($order['order_date'])) ?></p>
                        <p><strong>Người tạo:</strong> <?= htmlspecialchars($order['created_by_username']) ?></p>
                    </div>
                    <div class="col-md-6">
                        <p>
                            <strong>Trạng thái:</strong> 
                            <?php
                            switch ($order['status']) {
                                case 'pending':
                                    echo '<span class="badge bg-warning">Chờ xử lý</span>';
                                    break;
                                case 'processing':
                                    echo '<span class="badge bg-info">Đang xử lý</span>';
                                    break;
                                case 'completed':
                                    echo '<span class="badge bg-success">Hoàn thành</span>';
                                    break;
                                case 'cancelled':
                                    echo '<span class="badge bg-danger">Đã hủy</span>';
                                    break;
                            }
                            ?>
                        </p>
                        <?php if (!empty($order['notes'])): ?>
                            <p><strong>Ghi chú:</strong> <?= nl2br(htmlspecialchars($order['notes'])) ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Món ăn</th>
                                <th>Giá</th>
                                <th>Số lượng</th>
                                <th class="text-end">Thành tiền</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $details_result->data_seek(0); ?>
                            <?php while ($detail = $details_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?= htmlspecialchars($detail['food_name']) ?></td>
                                    <td><?= number_format($detail['unit_price'], 0, ',', '.') ?> đ</td>
                                    <td><?= $detail['quantity'] ?></td>
                                    <td class="text-end"><?= number_format($detail['subtotal'], 0, ',', '.') ?> đ</td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="3" class="text-end"><strong>Tổng tiền:</strong></td>
                                <td class="text-end"><?= number_format($order['total_amount'], 0, ',', '.') ?> đ</td>
                            </tr>
                            <?php if ($order['discount_percent'] > 0): ?>
                                <tr>
                                    <td colspan="3" class="text-end"><strong>Giảm giá (<?= $order['discount_percent'] ?>%):</strong></td>
                                    <td class="text-end">-<?= number_format($order['discount_amount'], 0, ',', '.') ?> đ</td>
                                </tr>
                            <?php endif; ?>
                            <tr>
                                <td colspan="3" class="text-end"><strong>Thành tiền:</strong></td>
                                <td class="text-end"><strong><?= number_format($order['final_amount'], 0, ',', '.') ?> đ</strong></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Table Transfer History -->
        <?php if ($transfer_result->num_rows > 0): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Lịch sử chuyển bàn</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Thời gian</th>
                                <th>Từ bàn</th>
                                <th>Đến bàn</th>
                                <th>Người chuyển</th>
                                <th>Lý do</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($transfer = $transfer_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?= date('d/m/Y H:i', strtotime($transfer['transfer_date'])) ?></td>
                                    <td><?= htmlspecialchars($transfer['from_table_number']) ?></td>
                                    <td><?= htmlspecialchars($transfer['to_table_number']) ?></td>
                                    <td><?= htmlspecialchars($transfer['transferred_by_username']) ?></td>
                                    <td><?= htmlspecialchars($transfer['reason']) ?></td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Payment Information -->
    <div class="col-md-4">
        <?php if ($payment): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Thông tin thanh toán</h5>
            </div>
            <div class="card-body">
                <p>
                    <strong>Trạng thái thanh toán:</strong> 
                    <?php
                    switch ($payment['payment_status']) {
                        case 'pending':
                            echo '<span class="badge bg-warning">Chờ xử lý</span>';
                            break;
                        case 'completed':
                            echo '<span class="badge bg-success">Đã thanh toán</span>';
                            break;
                        case 'failed':
                            echo '<span class="badge bg-danger">Thất bại</span>';
                            break;
                        case 'refunded':
                            echo '<span class="badge bg-info">Đã hoàn tiền</span>';
                            break;
                    }
                    ?>
                </p>
                <p><strong>Số tiền thanh toán:</strong> <?= number_format($payment['payment_amount'], 0, ',', '.') ?> đ</p>
                <p>
                    <strong>Phương thức thanh toán:</strong> 
                    <?php
                    switch ($payment['payment_method']) {
                        case 'cash':
                            echo 'Tiền mặt';
                            break;
                        case 'credit_card':
                            echo 'Thẻ tín dụng';
                            break;
                        case 'debit_card':
                            echo 'Thẻ ghi nợ';
                            break;
                        case 'mobile_payment':
                            echo 'Thanh toán di động';
                            break;
                    }
                    ?>
                </p>
                <p><strong>Thời gian thanh toán:</strong> <?= date('d/m/Y H:i:s', strtotime($payment['payment_date'])) ?></p>
                <p><strong>Người nhận thanh toán:</strong> <?= htmlspecialchars($payment['received_by_username']) ?></p>
                
                <?php if (!empty($payment['notes'])): ?>
                    <p><strong>Ghi chú:</strong> <?= nl2br(htmlspecialchars($payment['notes'])) ?></p>
                <?php endif; ?>
                
                <?php if ($payment['payment_status'] == 'completed'): ?>
                    <div class="text-center mt-4">
                        <div class="h1 text-success mb-3">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h5 class="text-success">Thanh toán thành công</h5>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <?php else: ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Thông tin thanh toán</h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="h1 text-warning mb-3">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <h5>Chưa thanh toán</h5>
                    <?php if ($order['status'] == 'pending' || $order['status'] == 'processing'): ?>
                        <a href="<?= STAFF_URL ?>/payment.php?id=<?= $order_id ?>" class="btn btn-success mt-3">
                            <i class="fas fa-cash-register me-2"></i>Thanh toán ngay
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>