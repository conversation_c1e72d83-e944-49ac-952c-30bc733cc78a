        </div>
    </div>
    
    <!-- Footer -->
    <footer class="bg-white text-center text-lg-start mt-5 border-top">
        <div class="text-center p-4">
            <div class="mb-1">
                <i class="fas fa-utensils text-primary me-2"></i>
                <span class="fw-bold">RestaurantPro</span>
            </div>
            <div class="text-muted">
                © <?= date('Y') ?> Hệ T<PERSON>ống Quản Lý Nhà Hàng | All rights reserved
            </div>
        </div>
    </footer>
    
    <!-- MDB JS -->
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.0/mdb.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?= ASSETS_URL ?>/js/script.js"></script>
    
    <!-- Offline Support -->
    <script src="<?= ASSETS_URL ?>/js/offline.js"></script>
    
    <?php if(isStaff() && isMobileDevice()): ?>
    <!-- Mobile Bottom Navigation -->
    <div class="mobile-bottom-nav d-md-none">
        <a href="<?= STAFF_URL ?>" class="mobile-bottom-nav-item <?= $current_page == 'index.php' ? 'active' : '' ?>">
            <i class="material-icons mobile-bottom-nav-icon">dashboard</i>
            <span class="mobile-bottom-nav-text">Trang chủ</span>
        </a>
        <a href="<?= STAFF_URL ?>/tables.php" class="mobile-bottom-nav-item <?= $current_page == 'tables.php' ? 'active' : '' ?>">
            <i class="material-icons mobile-bottom-nav-icon">table_restaurant</i>
            <span class="mobile-bottom-nav-text">Bàn</span>
        </a>
        <a href="<?= STAFF_URL ?>/new_order.php" class="mobile-bottom-nav-item <?= $current_page == 'new_order.php' ? 'active' : '' ?>">
            <i class="material-icons mobile-bottom-nav-icon">add_circle</i>
            <span class="mobile-bottom-nav-text">Tạo đơn</span>
        </a>
        <a href="<?= STAFF_URL ?>/orders.php" class="mobile-bottom-nav-item <?= $current_page == 'orders.php' ? 'active' : '' ?>">
            <i class="material-icons mobile-bottom-nav-icon">receipt_long</i>
            <span class="mobile-bottom-nav-text">Đơn hàng</span>
        </a>
        <a href="#userMenu" data-mdb-toggle="offcanvas" class="mobile-bottom-nav-item">
            <i class="material-icons mobile-bottom-nav-icon">person</i>
            <span class="mobile-bottom-nav-text">Tài khoản</span>
        </a>
    </div>
    
    <!-- User Menu Offcanvas -->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="userMenu" aria-labelledby="userMenuLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="userMenuLabel">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                        <i class="material-icons text-primary">person</i>
                    </div>
                    <div>
                        <div class="fw-bold"><?= $_SESSION['full_name'] ?? $_SESSION['username'] ?></div>
                        <small class="text-muted"><?= isAdmin() ? 'Quản trị viên' : 'Nhân viên' ?></small>
                    </div>
                </div>
            </h5>
            <button type="button" class="btn-close" data-mdb-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <div class="list-group list-group-flush">
                <a href="<?= SITE_URL ?>/profile.php" class="list-group-item list-group-item-action d-flex align-items-center">
                    <i class="material-icons me-3 text-info">account_circle</i>
                    <div>Thông tin cá nhân</div>
                </a>
                <a href="<?= SITE_URL ?>/change_password.php" class="list-group-item list-group-item-action d-flex align-items-center">
                    <i class="material-icons me-3 text-warning">vpn_key</i>
                    <div>Đổi mật khẩu</div>
                </a>
                <a href="<?= STAFF_URL ?>/daily_revenue.php" class="list-group-item list-group-item-action d-flex align-items-center">
                    <i class="material-icons me-3 text-success">payments</i>
                    <div>Doanh thu</div>
                </a>
                <div class="list-group-item d-flex align-items-center sync-button">
                    <i class="material-icons me-3 text-primary">sync</i>
                    <div>Đồng bộ dữ liệu</div>
                    <span class="pending-operations-badge" style="display: none;">0</span>
                </div>
                <a href="<?= SITE_URL ?>/auth/logout.php" class="list-group-item list-group-item-action d-flex align-items-center text-danger">
                    <i class="material-icons me-3">logout</i>
                    <div>Đăng xuất</div>
                </a>
            </div>
        </div>
    </div>
    
    <script>
        // Add body class for mobile nav spacing
        document.body.classList.add('has-mobile-nav');
        
        // Initialize sync button
        document.querySelector('.sync-button').addEventListener('click', function() {
            if (window.offlineManager) {
                window.offlineManager.syncPendingOperations();
            }
        });
    </script>
    <?php endif; ?>
    
    <!-- Modern UI JS -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize all tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-mdb-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new mdb.Tooltip(tooltipTriggerEl);
        });
        
        // Auto-initialize all form fields with values
        document.querySelectorAll('.form-outline').forEach((formOutline) => {
            const input = formOutline.querySelector('input, textarea');
            if (input && input.value) {
                input.classList.add('active');
            }
            new mdb.Input(formOutline).init();
        });
    });

    // Toast notification function
    function showToast(message, type = 'success') {
        const toastContainer = document.querySelector('.toast-container');
        const toastId = 'toast-' + Date.now();
        const bgClass = type === 'success' ? 'bg-success' : 
                       type === 'danger' ? 'bg-danger' : 
                       type === 'warning' ? 'bg-warning' : 'bg-info';
        const icon = type === 'success' ? 'check_circle' : 
                    type === 'danger' ? 'error' : 
                    type === 'warning' ? 'warning' : 'info';
        
        const toastHtml = `
            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-mdb-autohide="true" data-mdb-delay="3000">
                <div class="toast-header ${bgClass} text-white">
                    <i class="material-icons me-2">${icon}</i>
                    <strong class="me-auto">${type.charAt(0).toUpperCase() + type.slice(1)}</strong>
                    <button type="button" class="btn-close btn-close-white" data-mdb-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;
        
        toastContainer.insertAdjacentHTML('beforeend', toastHtml);
        const toastElement = document.getElementById(toastId);
        const toast = new mdb.Toast(toastElement);
        toast.show();
        
        // Remove toast after it's hidden
        toastElement.addEventListener('hidden.mdb.toast', function () {
            toastElement.remove();
        });
    }

    // Show/hide loading overlay
    function showLoading() {
        document.querySelector('.loading-overlay').classList.remove('d-none');
    }
    
    function hideLoading() {
        document.querySelector('.loading-overlay').classList.add('d-none');
    }

    // Function for category filtering in food items
    function filterFoodByCategory(categoryId) {
        const foodItems = document.querySelectorAll('.food-item-card');
        const filterButtons = document.querySelectorAll('.btn-outline-primary');
        
        // Reset active state on buttons
        filterButtons.forEach(button => {
            button.classList.remove('active');
        });
        
        // Set active state on clicked button
        event.currentTarget.classList.add('active');
        
        foodItems.forEach(item => {
            if (categoryId === 0 || parseInt(item.dataset.category) === categoryId) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }

    // Order management functions
    let orderItems = {}; // Store order items: {food_id: {name, price, quantity}}

    function addItemToOrder(foodId, foodName, price, quantity = 1) {
        if (orderItems[foodId]) {
            orderItems[foodId].quantity += 1;
        } else {
            orderItems[foodId] = {
                name: foodName,
                price: price,
                quantity: quantity
            };
        }
        updateOrderSummary();
    }

    function removeItemFromOrder(foodId) {
        delete orderItems[foodId];
        updateOrderSummary();
    }

    function changeItemQuantity(foodId, quantity) {
        if (quantity <= 0) {
            removeItemFromOrder(foodId);
        } else {
            orderItems[foodId].quantity = quantity;
            updateOrderSummary();
        }
    }

    function updateOrderSummary() {
        const orderItemsContainer = document.getElementById('orderItems');
        const subtotalElement = document.getElementById('subtotal');
        const subtotalInput = document.getElementById('subtotalInput');
        const discountPercentElement = document.getElementById('discountPercent');
        const discountAmountElement = document.getElementById('discountAmount');
        const discountAmountInput = document.getElementById('discountAmountInput');
        const finalAmountElement = document.getElementById('finalAmount');
        const finalAmountInput = document.getElementById('finalAmountInput');
        
        let html = '';
        let subtotal = 0;
        
        // Generate HTML for each item
        for (const [foodId, item] of Object.entries(orderItems)) {
            const itemTotal = item.price * item.quantity;
            subtotal += itemTotal;
            
            html += `
                <div class="order-item mb-3 border-bottom pb-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="fw-bold">${item.name}</div>
                            <div class="text-muted">${item.price.toLocaleString('vi-VN')} đ x ${item.quantity}</div>
                        </div>
                        <div>
                            <div class="fw-bold text-end mb-1">${itemTotal.toLocaleString('vi-VN')} đ</div>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" onclick="changeItemQuantity(${foodId}, ${item.quantity - 1})">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="removeItemFromOrder(${foodId})">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="changeItemQuantity(${foodId}, ${item.quantity + 1})">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" name="quantity[${foodId}]" value="${item.quantity}">
                    <input type="hidden" name="price[${foodId}]" value="${item.price}">
                </div>
            `;
        }
        
        if (html === '') {
            html = '<p class="text-muted text-center">Chưa có món ăn nào được chọn</p>';
        }
        
        orderItemsContainer.innerHTML = html;
        
        // Update totals
        subtotalElement.textContent = subtotal.toLocaleString('vi-VN') + ' đ';
        subtotalInput.value = subtotal;
        
        const discountPercent = parseFloat(discountPercentElement?.value || 0);
        const discountAmount = subtotal * (discountPercent / 100);
        
        if (discountAmountElement) {
            discountAmountElement.textContent = discountAmount.toLocaleString('vi-VN') + ' đ';
        }
        
        if (discountAmountInput) {
            discountAmountInput.value = discountAmount;
        }
        
        const finalAmount = subtotal - discountAmount;
        
        if (finalAmountElement) {
            finalAmountElement.textContent = finalAmount.toLocaleString('vi-VN') + ' đ';
        }
        
        if (finalAmountInput) {
            finalAmountInput.value = finalAmount;
        }
    }

    // Auto-refresh for real-time data every 30 seconds
    // This should only be enabled on pages that need real-time updates
    if (document.querySelector('.auto-refresh')) {
        setInterval(function() {
            location.reload();
        }, 30000); // 30 seconds
    }
    </script>
</body>
</html>