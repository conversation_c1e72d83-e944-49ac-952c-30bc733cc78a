<?php
require_once '../includes/config.php';

// Verify user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Get user ID from request or session
$user_id = isset($_POST['user_id']) ? (int)$_POST['user_id'] : $_SESSION['user_id'];

// Security check: ensure user can only mark their own notifications as read
if ($user_id != $_SESSION['user_id']) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Mark all notifications as read
$mark_read_query = "UPDATE notifications SET is_read = 1 WHERE user_id = ?";
$mark_read_stmt = $conn->prepare($mark_read_query);
$mark_read_stmt->bind_param("i", $user_id);
$success = $mark_read_stmt->execute();

// Log the activity
if ($success) {
    // Insert into activity_log table
    $activity_type = 'notifications_read';
    $description = 'User marked all notifications as read';
    
    $log_query = "INSERT INTO activity_log (user_id, activity_type, description) VALUES (?, ?, ?)";
    $log_stmt = $conn->prepare($log_query);
    $log_stmt->bind_param("iss", $user_id, $activity_type, $description);
    $log_stmt->execute();
}

// Return JSON response
echo json_encode([
    'success' => $success,
    'message' => $success ? 'All notifications marked as read' : 'Failed to update notifications',
    'affected_rows' => $mark_read_stmt->affected_rows
]);