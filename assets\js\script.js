/**
 * RestaurantPro - Modern JavaScript for restaurant management system
 * Enhanced with Material Design components and interactions
 */

// Initialize all components when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize core components
    initializeComponents();
    
    // Initialize modern animations
    initializeAnimations();
    
    // Initialize interactions
    initializeInteractions();
    
    // Initialize form validations
    initializeFormValidations();
    
    // Initialize realtime components if present
    initializeRealtimeComponents();
});

// Initialize MDB and custom components
function initializeComponents() {
    // Auto-initialize all Material Design components
    document.querySelectorAll('.form-outline').forEach((formOutline) => {
        new mdb.Input(formOutline).init();
    });
    
    // Initialize tooltips with modern styling
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-mdb-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new mdb.Tooltip(tooltipTriggerEl, {
            trigger: 'hover',
            animation: true,
            delay: {show: 100, hide: 100}
        });
    });

    // Initialize popovers with modern styling
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-mdb-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new mdb.Popover(popoverTriggerEl, {
            trigger: 'focus',
            animation: true
        });
    });
    
    // Auto dismiss alerts after 5 seconds with fade animation
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(function(alert) {
            const toast = new mdb.Toast(alert);
            toast.hide();
        });
    }, 5000);
    
    // Initialize search functionality
    initializeTableSearch();
    
    // Initialize date range filter
    initializeDateRangeFilter();
    
    // Initialize category filter
    initializeCategoryFilter();
    
    // Initialize image preview
    initializeImagePreview();
    
    // Initialize status filter
    initializeStatusFilter();
    
    // Initialize delete confirmations
    initializeDeleteConfirmations();
}

// Add modern animations and transitions
function initializeAnimations() {
    // Fade in animation for cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.4s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 50);
    });
}

// Initialize modern interactions
function initializeInteractions() {
    // Add keyboard navigation support
    document.addEventListener('keydown', function(e) {
        // ESC key closes modals
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                const modalInstance = mdb.Modal.getInstance(openModal);
                if (modalInstance) {
                    modalInstance.hide();
                }
            }
        }
        
        // ENTER key in search input triggers search
        if (e.key === 'Enter' && document.activeElement.classList.contains('search-input')) {
            e.preventDefault();
            const searchBtn = document.querySelector('.search-button');
            if (searchBtn) {
                searchBtn.click();
            }
        }
    });
    
    // Order item quantity change
    document.querySelectorAll('.quantity-input').forEach(function(input) {
        input.addEventListener('change', function() {
            updateOrderItemTotal(this);
        });
    });
    
    // Discount percentage change
    const discountInput = document.querySelector('#discountPercent');
    if (discountInput) {
        discountInput.addEventListener('input', function() {
            updateOrderTotal();
        });
    }
}

// Initialize form validations
function initializeFormValidations() {
    // Enhanced form validation with real-time feedback
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        // Add validation on submit
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                e.stopPropagation();
                showToast('Vui lòng kiểm tra lại các trường dữ liệu', 'warning');
            } else {
                // Show loading overlay for form submissions
                if (!this.classList.contains('no-loading')) {
                    showLoading();
                }
            }
            
            this.classList.add('was-validated');
        });
        
        // Add validation on field blur
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            // Clear validation on input
            input.addEventListener('input', function() {
                this.classList.remove('is-invalid');
                const errorMsg = this.parentNode.querySelector('.invalid-feedback');
                if (errorMsg) {
                    errorMsg.remove();
                }
            });
        });
    });
}

// Initialize realtime components
function initializeRealtimeComponents() {
    // Setup auto-refresh for tables that need it
    if (document.querySelector('.auto-refresh')) {
        setInterval(function() {
            refreshData();
        }, 30000); // 30 seconds
    }
}

// Validate form
function validateForm(form) {
    let isValid = true;
    const inputs = form.querySelectorAll('input, select, textarea');
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    return isValid;
}

// Validate field
function validateField(field) {
    // Skip disabled or optional fields
    if (field.disabled || !field.required) {
        return true;
    }
    
    const value = field.value.trim();
    let isValid = true;
    let message = '';
    
    // Check required fields
    if (field.required && value === '') {
        isValid = false;
        message = 'Trường này không được để trống';
    }
    // Email validation
    else if (field.type === 'email' && value !== '' && !isValidEmail(value)) {
        isValid = false;
        message = 'Địa chỉ email không hợp lệ';
    }
    // Password validation
    else if (field.type === 'password' && field.minLength && value.length < field.minLength) {
        isValid = false;
        message = `Mật khẩu phải có ít nhất ${field.minLength} ký tự`;
    }
    // Number validation
    else if (field.type === 'number') {
        const num = parseFloat(value);
        if (isNaN(num)) {
            isValid = false;
            message = 'Vui lòng nhập một số hợp lệ';
        } else if (field.min !== undefined && num < parseFloat(field.min)) {
            isValid = false;
            message = `Giá trị phải lớn hơn hoặc bằng ${field.min}`;
        } else if (field.max !== undefined && num > parseFloat(field.max)) {
            isValid = false;
            message = `Giá trị phải nhỏ hơn hoặc bằng ${field.max}`;
        }
    }
    
    // Update validation UI
    field.classList.remove('is-valid', 'is-invalid');
    
    // Remove existing feedback
    const existingFeedback = field.parentNode.querySelector('.invalid-feedback');
    if (existingFeedback) {
        existingFeedback.remove();
    }
    
    if (!isValid) {
        field.classList.add('is-invalid');
        
        // Add error message
        const feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        feedback.innerHTML = message;
        field.parentNode.appendChild(feedback);
    } else if (value !== '') {
        field.classList.add('is-valid');
    }
    
    return isValid;
}

// Email validation helper
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Show/hide loading overlay
function showLoading() {
    document.querySelector('.loading-overlay').classList.remove('d-none');
}

function hideLoading() {
    document.querySelector('.loading-overlay').classList.add('d-none');
}

// Toast notification function
function showToast(message, type = 'success') {
    const toastContainer = document.querySelector('.toast-container');
    const toastId = 'toast-' + Date.now();
    const bgClass = type === 'success' ? 'bg-success' : 
                   type === 'danger' ? 'bg-danger' : 
                   type === 'warning' ? 'bg-warning' : 'bg-info';
    const icon = type === 'success' ? 'check_circle' : 
                type === 'danger' ? 'error' : 
                type === 'warning' ? 'warning' : 'info';
    
    const toastHtml = `
        <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-mdb-autohide="true" data-mdb-delay="3000">
            <div class="toast-header ${bgClass} text-white">
                <i class="material-icons me-2">${icon}</i>
                <strong class="me-auto">${type.charAt(0).toUpperCase() + type.slice(1)}</strong>
                <button type="button" class="btn-close btn-close-white" data-mdb-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    const toastElement = document.getElementById(toastId);
    const toast = new mdb.Toast(toastElement);
    toast.show();
    
    // Remove toast after it's hidden
    toastElement.addEventListener('hidden.mdb.toast', function () {
        toastElement.remove();
    });
}

// Search functionality for tables
function initializeTableSearch() {
    const searchInput = document.getElementById('tableSearch');
    if (!searchInput) return;
    
    searchInput.addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('table tbody tr');
        
        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
}

// Date range filter for reports
function initializeDateRangeFilter() {
    const startDateInput = document.getElementById('startDate');
    const endDateInput = document.getElementById('endDate');
    const applyFilterBtn = document.getElementById('applyDateFilter');
    
    if (!startDateInput || !endDateInput || !applyFilterBtn) return;
    
    applyFilterBtn.addEventListener('click', function() {
        const startDate = startDateInput.value;
        const endDate = endDateInput.value;
        
        if (!startDate || !endDate) {
            showToast('Vui lòng chọn ngày bắt đầu và kết thúc', 'warning');
            return;
        }
        
        if (new Date(startDate) > new Date(endDate)) {
            showToast('Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc', 'warning');
            return;
        }
        
        // Update URL with new date range
        const currentUrl = new URL(window.location.href);
        currentUrl.searchParams.set('start_date', startDate);
        currentUrl.searchParams.set('end_date', endDate);
        
        window.location.href = currentUrl.toString();
    });
}

// Category filter for food items
function initializeCategoryFilter() {
    const categorySelect = document.getElementById('categoryFilter');
    if (!categorySelect) return;
    
    categorySelect.addEventListener('change', function() {
        const categoryId = this.value;
        const foodItems = document.querySelectorAll('.food-item-card');
        
        foodItems.forEach(item => {
            if (categoryId === '0' || item.dataset.category === categoryId) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    });
}

// Image preview functionality
function initializeImagePreview() {
    const imageInput = document.getElementById('image');
    const imagePreview = document.getElementById('imagePreview');
    
    if (!imageInput || !imagePreview) return;
    
    imageInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                imagePreview.src = e.target.result;
                imagePreview.style.display = 'block';
            };
            
            reader.readAsDataURL(this.files[0]);
        } else {
            imagePreview.style.display = 'none';
        }
    });
}

// Status filter for orders
function initializeStatusFilter() {
    const statusSelect = document.getElementById('statusFilter');
    if (!statusSelect) return;
    
    statusSelect.addEventListener('change', function() {
        const status = this.value;
        const orderRows = document.querySelectorAll('table tbody tr');
        
        orderRows.forEach(row => {
            if (status === 'all' || row.dataset.status === status) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
}

// Delete confirmation with modal
function initializeDeleteConfirmations() {
    const deleteButtons = document.querySelectorAll('.btn-delete');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (!confirm('Bạn có chắc chắn muốn xóa mục này không?')) {
                e.preventDefault();
            }
        });
    });
}

// Refresh current page data
function refreshData() {
    // This can be customized to only refresh specific content instead of reloading
    location.reload();
}

// Function for category filtering in food items
function filterFoodByCategory(categoryId) {
    const foodItems = document.querySelectorAll('.food-item-card');
    const filterButtons = document.querySelectorAll('.btn-outline-primary');
    
    // Reset active state on buttons
    filterButtons.forEach(button => {
        button.classList.remove('active');
    });
    
    // Set active state on clicked button
    event.currentTarget.classList.add('active');
    
    foodItems.forEach(item => {
        if (categoryId === 0 || parseInt(item.dataset.category) === categoryId) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
}

// Order management functions
let orderItems = {}; // Store order items: {food_id: {name, price, quantity}}

function addItemToOrder(foodId, foodName, price, quantity = 1) {
    if (orderItems[foodId]) {
        orderItems[foodId].quantity += 1;
    } else {
        orderItems[foodId] = {
            name: foodName,
            price: price,
            quantity: quantity
        };
    }
    updateOrderSummary();
}

function removeItemFromOrder(foodId) {
    delete orderItems[foodId];
    updateOrderSummary();
}

function changeItemQuantity(foodId, quantity) {
    if (quantity <= 0) {
        removeItemFromOrder(foodId);
    } else {
        orderItems[foodId].quantity = quantity;
        updateOrderSummary();
    }
}

function updateOrderSummary() {
    const orderItemsContainer = document.getElementById('orderItems');
    const subtotalElement = document.getElementById('subtotal');
    const subtotalInput = document.getElementById('subtotalInput');
    const discountPercentElement = document.getElementById('discountPercent');
    const discountAmountElement = document.getElementById('discountAmount');
    const discountAmountInput = document.getElementById('discountAmountInput');
    const finalAmountElement = document.getElementById('finalAmount');
    const finalAmountInput = document.getElementById('finalAmountInput');
    
    let html = '';
    let subtotal = 0;
    
    // Generate HTML for each item
    for (const [foodId, item] of Object.entries(orderItems)) {
        const itemTotal = item.price * item.quantity;
        subtotal += itemTotal;
        
        html += `
            <div class="order-item mb-3 border-bottom pb-2">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="fw-bold">${item.name}</div>
                        <div class="text-muted">${item.price.toLocaleString('vi-VN')} đ x ${item.quantity}</div>
                    </div>
                    <div>
                        <div class="fw-bold text-end mb-1">${itemTotal.toLocaleString('vi-VN')} đ</div>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-primary" onclick="changeItemQuantity(${foodId}, ${item.quantity - 1})">
                                <i class="fas fa-minus"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="removeItemFromOrder(${foodId})">
                                <i class="fas fa-trash"></i>
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="changeItemQuantity(${foodId}, ${item.quantity + 1})">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <input type="hidden" name="quantity[${foodId}]" value="${item.quantity}">
                <input type="hidden" name="price[${foodId}]" value="${item.price}">
            </div>
        `;
    }
    
    if (html === '') {
        html = '<p class="text-muted text-center">Chưa có món ăn nào được chọn</p>';
    }
    
    orderItemsContainer.innerHTML = html;
    
    // Update totals
    subtotalElement.textContent = subtotal.toLocaleString('vi-VN') + ' đ';
    subtotalInput.value = subtotal;
    
    const discountPercent = parseFloat(discountPercentElement?.value || 0);
    const discountAmount = subtotal * (discountPercent / 100);
    
    if (discountAmountElement) {
        discountAmountElement.textContent = discountAmount.toLocaleString('vi-VN') + ' đ';
    }
    
    if (discountAmountInput) {
        discountAmountInput.value = discountAmount;
    }
    
    const finalAmount = subtotal - discountAmount;
    
    if (finalAmountElement) {
        finalAmountElement.textContent = finalAmount.toLocaleString('vi-VN') + ' đ';
    }
    
    if (finalAmountInput) {
        finalAmountInput.value = finalAmount;
    }
}

// Update order item total when quantity changes
function updateOrderItemTotal(input) {
    const quantity = parseInt(input.value);
    const price = parseFloat(input.getAttribute('data-price'));
    const totalElement = document.querySelector('#item-total-' + input.getAttribute('data-item-id'));
    
    if (totalElement) {
        const total = quantity * price;
        totalElement.textContent = total.toLocaleString('vi-VN') + ' đ';
        updateOrderTotal();
    }
}

// Update order total amount
function updateOrderTotal() {
    let subtotal = 0;
    document.querySelectorAll('.item-subtotal').forEach(function(element) {
        const value = element.textContent.replace(/[^\d]/g, '');
        subtotal += parseInt(value);
    });
    
    const discountPercent = parseFloat(document.querySelector('#discountPercent').value) || 0;
    const discountAmount = subtotal * (discountPercent / 100);
    const finalAmount = subtotal - discountAmount;
    
    document.querySelector('#subtotal').textContent = subtotal.toLocaleString('vi-VN') + ' đ';
    document.querySelector('#discountAmount').textContent = discountAmount.toLocaleString('vi-VN') + ' đ';
    document.querySelector('#finalAmount').textContent = finalAmount.toLocaleString('vi-VN') + ' đ';
    
    // Update hidden input for form submission
    document.querySelector('#subtotalInput').value = subtotal;
    document.querySelector('#discountAmountInput').value = discountAmount;
    document.querySelector('#finalAmountInput').value = finalAmount;
}