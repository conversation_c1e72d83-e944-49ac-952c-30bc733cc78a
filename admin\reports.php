<?php
require_once '../includes/header.php';

// Check if user is admin
if (!isAdmin()) {
    setFlashMessage('danger', 'Bạn không có quyền truy cập trang này');
    redirect(SITE_URL);
}

// Default report type
$report_type = isset($_GET['type']) ? sanitize($_GET['type']) : 'daily';
$valid_types = ['daily', 'weekly', 'monthly'];

if (!in_array($report_type, $valid_types)) {
    $report_type = 'daily';
}

// Date range
$start_date = isset($_GET['start_date']) ? sanitize($_GET['start_date']) : date('Y-m-d', strtotime('-30 days'));
$end_date = isset($_GET['end_date']) ? sanitize($_GET['end_date']) : date('Y-m-d');

// Get revenue data
$sql = '';
$group_by = '';
$date_format = '';

switch ($report_type) {
    case 'daily':
        $date_format = '%d/%m/%Y';
        $group_by = 'DATE(payment_date)';
        break;
    case 'weekly':
        $date_format = 'Tuần %u, %Y';
        $group_by = 'YEARWEEK(payment_date, 1)';
        break;
    case 'monthly':
        $date_format = '%m/%Y';
        $group_by = 'YEAR(payment_date), MONTH(payment_date)';
        break;
}

$sql = "SELECT 
            DATE_FORMAT(payment_date, '$date_format') as period,
            SUM(payment_amount) as total_revenue,
            COUNT(DISTINCT order_id) as order_count
        FROM 
            payments
        WHERE 
            payment_status = 'completed'
            AND DATE(payment_date) BETWEEN ? AND ?
        GROUP BY 
            $group_by
        ORDER BY 
            payment_date";

$stmt = $conn->prepare($sql);
$stmt->bind_param("ss", $start_date, $end_date);
$stmt->execute();
$result = $stmt->get_result();

// Get popular food items
$popular_sql = "SELECT 
                    f.food_id,
                    f.food_name,
                    c.category_name,
                    SUM(od.quantity) as total_quantity,
                    SUM(od.subtotal) as total_revenue
                FROM 
                    order_details od
                JOIN 
                    food_items f ON od.food_id = f.food_id
                JOIN 
                    categories c ON f.category_id = c.category_id
                JOIN 
                    orders o ON od.order_id = o.order_id
                JOIN 
                    payments p ON o.order_id = p.order_id
                WHERE 
                    p.payment_status = 'completed'
                    AND DATE(p.payment_date) BETWEEN ? AND ?
                GROUP BY 
                    f.food_id, f.food_name, c.category_name
                ORDER BY 
                    total_quantity DESC
                LIMIT 10";

$popular_stmt = $conn->prepare($popular_sql);
$popular_stmt->bind_param("ss", $start_date, $end_date);
$popular_stmt->execute();
$popular_result = $popular_stmt->get_result();

// Get category statistics
$category_sql = "SELECT 
                    c.category_name,
                    SUM(od.quantity) as total_quantity,
                    SUM(od.subtotal) as total_revenue
                FROM 
                    order_details od
                JOIN 
                    food_items f ON od.food_id = f.food_id
                JOIN 
                    categories c ON f.category_id = c.category_id
                JOIN 
                    orders o ON od.order_id = o.order_id
                JOIN 
                    payments p ON o.order_id = p.order_id
                WHERE 
                    p.payment_status = 'completed'
                    AND DATE(p.payment_date) BETWEEN ? AND ?
                GROUP BY 
                    c.category_id, c.category_name
                ORDER BY 
                    total_revenue DESC";

$category_stmt = $conn->prepare($category_sql);
$category_stmt->bind_param("ss", $start_date, $end_date);
$category_stmt->execute();
$category_result = $category_stmt->get_result();

// Calculate total revenue
$total_revenue = 0;
$total_orders = 0;
$revenue_data = [];

if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $total_revenue += $row['total_revenue'];
        $total_orders += $row['order_count'];
        $revenue_data[] = $row;
    }
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h2>Báo cáo doanh thu</h2>
    </div>
</div>

<!-- Filter Form -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" action="">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label class="form-label">Loại báo cáo</label>
                    <select class="form-select" name="type" onchange="this.form.submit()">
                        <option value="daily" <?= $report_type == 'daily' ? 'selected' : '' ?>>Theo ngày</option>
                        <option value="weekly" <?= $report_type == 'weekly' ? 'selected' : '' ?>>Theo tuần</option>
                        <option value="monthly" <?= $report_type == 'monthly' ? 'selected' : '' ?>>Theo tháng</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Từ ngày</label>
                    <input type="date" class="form-control" name="start_date" value="<?= $start_date ?>" max="<?= date('Y-m-d') ?>">
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Đến ngày</label>
                    <input type="date" class="form-control" name="end_date" value="<?= $end_date ?>" max="<?= date('Y-m-d') ?>">
                </div>
                <div class="col-md-3 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">Áp dụng</button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title">Tổng doanh thu</h5>
                <h2 class="text-primary"><?= number_format($total_revenue, 0, ',', '.') ?> đ</h2>
                <p class="text-muted">Từ <?= date('d/m/Y', strtotime($start_date)) ?> đến <?= date('d/m/Y', strtotime($end_date)) ?></p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title">Tổng số đơn hàng</h5>
                <h2 class="text-success"><?= $total_orders ?></h2>
                <p class="text-muted">Đơn hàng đã hoàn thành</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title">Trung bình mỗi đơn</h5>
                <h2 class="text-info"><?= $total_orders > 0 ? number_format($total_revenue / $total_orders, 0, ',', '.') : 0 ?> đ</h2>
                <p class="text-muted">Giá trị trung bình</p>
            </div>
        </div>
    </div>
</div>

<!-- Revenue Chart -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Biểu đồ doanh thu <?= $report_type == 'daily' ? 'theo ngày' : ($report_type == 'weekly' ? 'theo tuần' : 'theo tháng') ?></h5>
    </div>
    <div class="card-body">
        <?php if (count($revenue_data) > 0): ?>
            <canvas id="revenueChart" height="300"></canvas>
        <?php else: ?>
            <p class="text-center">Không có dữ liệu doanh thu trong khoảng thời gian này</p>
        <?php endif; ?>
    </div>
</div>

<div class="row">
    <!-- Popular Items -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Món ăn bán chạy</h5>
            </div>
            <div class="card-body">
                <?php if ($popular_result->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Món ăn</th>
                                    <th>Danh mục</th>
                                    <th>Số lượng</th>
                                    <th>Doanh thu</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($item = $popular_result->fetch_assoc()): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($item['food_name']) ?></td>
                                        <td><?= htmlspecialchars($item['category_name']) ?></td>
                                        <td><?= $item['total_quantity'] ?></td>
                                        <td><?= number_format($item['total_revenue'], 0, ',', '.') ?> đ</td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-center">Không có dữ liệu món ăn trong khoảng thời gian này</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Category Statistics -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Thống kê theo danh mục</h5>
            </div>
            <div class="card-body">
                <?php if ($category_result->num_rows > 0): ?>
                    <canvas id="categoryChart" height="300"></canvas>
                <?php else: ?>
                    <p class="text-center">Không có dữ liệu danh mục trong khoảng thời gian này</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Revenue Data Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Chi tiết doanh thu</h5>
    </div>
    <div class="card-body">
        <?php if (count($revenue_data) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Thời gian</th>
                            <th>Số đơn hàng</th>
                            <th>Doanh thu</th>
                            <th>Trung bình/đơn</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($revenue_data as $data): ?>
                            <tr>
                                <td><?= $data['period'] ?></td>
                                <td><?= $data['order_count'] ?></td>
                                <td><?= number_format($data['total_revenue'], 0, ',', '.') ?> đ</td>
                                <td><?= number_format($data['total_revenue'] / $data['order_count'], 0, ',', '.') ?> đ</td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <p class="text-center">Không có dữ liệu doanh thu trong khoảng thời gian này</p>
        <?php endif; ?>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php if (count($revenue_data) > 0): ?>
    // Revenue Chart
    var revenueCtx = document.getElementById('revenueChart').getContext('2d');
    var revenueChart = new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: [<?= implode(', ', array_map(function($data) { return "'" . $data['period'] . "'"; }, $revenue_data)) ?>],
            datasets: [{
                label: 'Doanh thu',
                data: [<?= implode(', ', array_map(function($data) { return $data['total_revenue']; }, $revenue_data)) ?>],
                backgroundColor: 'rgba(13, 110, 253, 0.2)',
                borderColor: 'rgba(13, 110, 253, 1)',
                borderWidth: 2,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString('vi-VN') + ' đ';
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': ' + context.raw.toLocaleString('vi-VN') + ' đ';
                        }
                    }
                }
            }
        }
    });
    <?php endif; ?>

    <?php if ($category_result->num_rows > 0): ?>
    // Category Chart
    var categoryCtx = document.getElementById('categoryChart').getContext('2d');
    var categoryChart = new Chart(categoryCtx, {
        type: 'pie',
        data: {
            labels: [
                <?php 
                $category_result->data_seek(0);
                while ($category = $category_result->fetch_assoc()) {
                    echo "'" . htmlspecialchars($category['category_name']) . "', ";
                }
                ?>
            ],
            datasets: [{
                data: [
                    <?php 
                    $category_result->data_seek(0);
                    while ($category = $category_result->fetch_assoc()) {
                        echo $category['total_revenue'] . ", ";
                    }
                    ?>
                ],
                backgroundColor: [
                    'rgba(13, 110, 253, 0.7)',
                    'rgba(25, 135, 84, 0.7)',
                    'rgba(220, 53, 69, 0.7)',
                    'rgba(255, 193, 7, 0.7)',
                    'rgba(13, 202, 240, 0.7)',
                    'rgba(111, 66, 193, 0.7)',
                    'rgba(253, 126, 20, 0.7)',
                    'rgba(32, 201, 151, 0.7)',
                    'rgba(108, 117, 125, 0.7)',
                    'rgba(173, 181, 189, 0.7)'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'right',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            var label = context.label || '';
                            var value = context.raw.toLocaleString('vi-VN') + ' đ';
                            var percentage = Math.round((context.raw / <?= $total_revenue ?>) * 100) + '%';
                            return label + ': ' + value + ' (' + percentage + ')';
                        }
                    }
                }
            }
        }
    });
    <?php endif; ?>
});
</script>

<?php require_once '../includes/footer.php'; ?>