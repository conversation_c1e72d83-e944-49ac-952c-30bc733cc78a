/**
 * Mobile-optimized styles for Restaurant Management System
 * This file contains responsive enhancements and mobile-specific styles
 */

/* Mobile navigation improvements */
@media (max-width: 991.98px) {
    /* Make the navbar more compact on mobile */
    .navbar-brand .brand-text {
        font-size: 1.1rem;
    }
    
    /* Improve the mobile menu */
    .navbar-collapse {
        background-color: white;
        border-radius: 0.5rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        max-height: 80vh;
        overflow-y: auto;
        position: absolute;
        top: 60px;
        left: 0;
        right: 0;
        z-index: 1030;
        margin: 0 1rem;
    }
    
    .navbar-nav .nav-link {
        padding: 0.8rem 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .navbar-nav .dropdown-menu {
        box-shadow: none;
        border: none;
        padding-left: 1.5rem;
    }
    
    .navbar-nav .dropdown-item {
        padding: 0.8rem 1rem;
    }
    
    /* Adjust dropdown header for mobile */
    .dropdown-header {
        padding: 0.8rem 1rem;
    }
}

/* Dashboard responsiveness for smaller screens */
@media (max-width: 767.98px) {
    .date-time-display {
        text-align: left;
        margin-top: 1rem;
    }
    
    .welcome-card {
        padding: 1rem;
    }
    
    .quick-actions {
        justify-content: center;
        margin-top: 1.5rem;
    }
    
    .dashboard-card-value {
        font-size: 1.5rem;
    }
    
    .card-title {
        font-size: 1rem;
    }
    
    /* Adjust spacing on mobile */
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    /* Adjust table status cards */
    .table-status-card {
        padding: 0.75rem;
    }
    
    .table-status-card .fs-4 {
        font-size: 1.2rem !important;
    }
    
    /* Make filter controls more mobile-friendly */
    .filter-controls .form-select,
    .filter-controls .form-control {
        margin-bottom: 0.5rem;
    }
    
    /* Improved buttons on mobile */
    .btn-group {
        display: flex;
        width: 100%;
    }
    
    .btn-group .btn {
        flex: 1;
    }
    
    /* Floating action button for creating new items */
    .fab-container {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        z-index: 999;
    }
    
    .fab {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 6px 10px rgba(0, 0, 0, 0.2);
    }
    
    .fab i {
        font-size: 24px;
    }
    
    /* Table view optimization for mobile */
    .mobile-card-view .card {
        margin-bottom: 1rem;
    }
    
    .mobile-card-view .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .mobile-card-view .card-body {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }
    
    /* Hide tables on mobile and show card view instead */
    @media (max-width: 767.98px) {
        .desktop-table-view {
            display: none;
        }
        
        .mobile-card-view {
            display: block;
        }
    }
    
    @media (min-width: 768px) {
        .desktop-table-view {
            display: block;
        }
        
        .mobile-card-view {
            display: none;
        }
    }
}

/* Enhanced touch controls for mobile */
@media (max-width: 767.98px) {
    /* Make buttons larger for touch */
    .btn {
        padding: 0.5rem 1rem;
        font-size: 1rem;
    }
    
    .btn-sm {
        padding: 0.375rem 0.75rem;
    }
    
    /* Improve form controls */
    .form-control, 
    .form-select {
        height: 48px;
        font-size: 1rem;
    }
    
    /* Improve checkbox/radio size */
    .form-check-input {
        width: 1.2em;
        height: 1.2em;
    }
    
    /* Larger touch targets */
    .list-group-item {
        padding: 1rem;
    }
    
    /* Toast notifications positioning for mobile */
    .toast-container {
        bottom: 4rem;
        right: 1rem;
        left: 1rem;
    }
    
    /* Adjust modal sizing */
    .modal-dialog {
        margin: 0.5rem;
    }
    
    /* Mobile-specific card layouts */
    .mobile-list-card {
        padding: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        background-color: white;
        margin-bottom: 1rem;
        position: relative;
    }
    
    .mobile-list-card .title {
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
    
    .mobile-list-card .subtitle {
        color: #6c757d;
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }
    
    .mobile-list-card .badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
    }
    
    .mobile-list-card .actions {
        margin-top: 1rem;
        display: flex;
        justify-content: flex-end;
        gap: 0.5rem;
    }
}

/* Device-specific optimizations */
/* iPhone SE, smaller devices */
@media (max-width: 374.98px) {
    body {
        font-size: 0.875rem;
    }
    
    .dashboard-card-title {
        font-size: 0.8rem;
    }
    
    .dashboard-card-value {
        font-size: 1.25rem;
    }
    
    .dashboard-icon {
        width: 40px;
        height: 40px;
    }
    
    .dashboard-icon i {
        font-size: 20px;
    }
    
    .card-title {
        font-size: 0.9rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    .quick-action-icon {
        width: 40px;
        height: 40px;
    }
    
    .quick-action-text {
        font-size: 0.7rem;
    }
}

/* Better scrolling experience on mobile */
@media (max-width: 767.98px) {
    .table-responsive {
        -webkit-overflow-scrolling: touch;
    }
    
    /* Pull-to-refresh indicator */
    .ptr-element {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        color: #3b71ca;
        z-index: 10;
        text-align: center;
        height: 50px;
    }
    
    .ptr-element .ptr-icon {
        transform-origin: 50% 0;
        transition: transform 0.25s;
        margin-top: 10px;
    }
    
    .ptr-refresh .ptr-icon {
        transform: rotate(180deg);
    }
    
    .ptr-loading .ptr-icon {
        display: none;
    }
    
    .ptr-loading .ptr-spinner {
        display: block;
        margin: 0 auto;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border: 3px solid #3b71ca;
        border-top-color: transparent;
        animation: ptr-spin 0.8s linear infinite;
    }
    
    @keyframes ptr-spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }
}

/* Dark mode support for mobile */
@media (prefers-color-scheme: dark) {
    .dark-mode {
        --bg-color: #121212;
        --text-color: #e0e0e0;
        --card-bg: #1e1e1e;
        --border-color: #333;
        --header-bg: #1a1a1a;
        
        background-color: var(--bg-color);
        color: var(--text-color);
    }
    
    .dark-mode .card {
        background-color: var(--card-bg);
        border-color: var(--border-color);
    }
    
    .dark-mode .navbar {
        background-color: var(--header-bg) !important;
    }
    
    .dark-mode .text-muted {
        color: #adb5bd !important;
    }
    
    .dark-mode .bg-white {
        background-color: var(--card-bg) !important;
    }
    
    .dark-mode .border-light {
        border-color: var(--border-color) !important;
    }
    
    /* Adjust form controls for dark mode */
    .dark-mode .form-control,
    .dark-mode .form-select {
        background-color: #2c2c2c;
        border-color: #444;
        color: var(--text-color);
    }
    
    .dark-mode .form-control:focus,
    .dark-mode .form-select:focus {
        background-color: #333;
    }
    
    /* Adjust table styling for dark mode */
    .dark-mode .table {
        color: var(--text-color);
    }
    
    .dark-mode .table-light th {
        background-color: #2c2c2c !important;
        color: var(--text-color);
    }
    
    .dark-mode .table-hover tbody tr:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }
}

/* Offline indicator styles */
.offline-indicator {
    position: fixed;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    background-color: #dc3545;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    z-index: 9999;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s;
}

.offline-indicator.visible {
    opacity: 1;
}

.offline-indicator .material-icons {
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

/* Enhanced Mobile Layouts */
@media (max-width: 767.98px) {
    /* Food menu mobile optimization */
    .food-item-card {
        margin-bottom: 1rem;
    }
    
    .food-item-image {
        height: 120px;
        object-fit: cover;
    }
    
    /* Order create/edit mobile optimization */
    .order-sidebar {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1030;
        background-color: white;
        padding: 1rem;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        max-height: 60vh;
        overflow-y: auto;
        transform: translateY(100%);
        transition: transform 0.3s;
    }
    
    .order-sidebar.show {
        transform: translateY(0);
    }
    
    .order-toggle-btn {
        position: fixed;
        bottom: 1rem;
        right: 1rem;
        z-index: 1031;
        width: 56px;
        height: 56px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    /* Mobile table management */
    .table-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
    
    /* Bottom navigation for mobile */
    .mobile-bottom-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: white;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: space-around;
        padding: 0.5rem 0;
        z-index: 1030;
    }
    
    .mobile-bottom-nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: #6c757d;
    }
    
    .mobile-bottom-nav-item.active {
        color: #3b71ca;
    }
    
    .mobile-bottom-nav-icon {
        font-size: 1.5rem;
    }
    
    .mobile-bottom-nav-text {
        font-size: 0.7rem;
        margin-top: 0.25rem;
    }
    
    /* Add padding to main content to account for bottom nav */
    body.has-mobile-nav #main-content {
        padding-bottom: 4rem;
    }
    
    /* Mobile-optimized dashboard */
    .mobile-dash-card {
        border-radius: 1rem;
        overflow: hidden;
        margin-bottom: 1rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    
    .mobile-dash-card-header {
        padding: 1rem;
        background-color: #f8f9fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .mobile-dash-card-body {
        padding: 1rem;
    }
    
    .mobile-dash-card-footer {
        padding: 0.75rem 1rem;
        background-color: #f8f9fa;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }
}

/* Animations for mobile transitions */
.slide-in-right {
    animation: slideInRight 0.3s forwards;
}

.slide-out-right {
    animation: slideOutRight 0.3s forwards;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(100%);
    }
}

/* Mobile device detection and adaptations */
@media (pointer: coarse) {
    /* Better controls for touch devices */
    .table-row-action-btn {
        opacity: 1 !important;
    }
    
    .form-range {
        height: 2rem;
    }
    
    /* Gesture hints */
    .gesture-hint {
        position: fixed;
        bottom: 3rem;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        font-size: 0.8rem;
        z-index: 1040;
        animation: fadeOutUp 3s forwards;
    }
    
    @keyframes fadeOutUp {
        0% {
            opacity: 1;
            transform: translate(-50%, 0);
        }
        80% {
            opacity: 1;
            transform: translate(-50%, 0);
        }
        100% {
            opacity: 0;
            transform: translate(-50%, -20px);
        }
    }
}