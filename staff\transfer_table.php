<?php
require_once '../includes/header.php';

// Check if user is staff
if (!isStaff()) {
    setFlashMessage('danger', 'Bạn không có quyền truy cập trang này');
    redirect(SITE_URL);
}

// Check if order ID is provided
if (!isset($_GET['order_id']) || empty($_GET['order_id'])) {
    setFlashMessage('danger', 'ID đơn hàng không hợp lệ');
    redirect(STAFF_URL . '/tables.php');
}

$order_id = (int)$_GET['order_id'];

// Get order information
$order_sql = "SELECT o.*, t.table_number 
              FROM orders o
              JOIN tables t ON o.table_id = t.table_id
              WHERE o.order_id = ?";
$order_stmt = $conn->prepare($order_sql);
$order_stmt->bind_param("i", $order_id);
$order_stmt->execute();
$order_result = $order_stmt->get_result();

if ($order_result->num_rows == 0) {
    setFlashMessage('danger', 'Không tìm thấy đơn hàng');
    redirect(STAFF_URL . '/tables.php');
}

$order = $order_result->fetch_assoc();

// Check if order is editable (pending or processing)
if ($order['status'] != 'pending' && $order['status'] != 'processing') {
    setFlashMessage('danger', 'Không thể chuyển bàn cho đơn hàng đã hoàn thành hoặc đã hủy');
    redirect(STAFF_URL . '/tables.php');
}

// Get all available tables
$tables_sql = "SELECT * FROM tables WHERE status = 'available' OR table_id = ? ORDER BY table_number";
$tables_stmt = $conn->prepare($tables_sql);
$tables_stmt->bind_param("i", $order['table_id']);
$tables_stmt->execute();
$tables_result = $tables_stmt->get_result();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $to_table_id = (int)$_POST['to_table_id'];
    $reason = sanitize($_POST['reason']);
    $error = '';
    
    // Validate inputs
    if ($to_table_id <= 0) {
        $error = 'Vui lòng chọn bàn để chuyển đến';
    } elseif ($to_table_id == $order['table_id']) {
        $error = 'Bàn chuyển đến không thể giống bàn hiện tại';
    } else {
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Get destination table info
            $to_table_sql = "SELECT status FROM tables WHERE table_id = ?";
            $to_table_stmt = $conn->prepare($to_table_sql);
            $to_table_stmt->bind_param("i", $to_table_id);
            $to_table_stmt->execute();
            $to_table_result = $to_table_stmt->get_result();
            
            if ($to_table_result->num_rows == 0) {
                throw new Exception('Không tìm thấy bàn đích');
            }
            
            $to_table = $to_table_result->fetch_assoc();
            
            if ($to_table['status'] != 'available' && $to_table['status'] != 'reserved') {
                throw new Exception('Bàn đích đang được sử dụng hoặc bảo trì');
            }
            
            // Update from table status to available
            $update_from_sql = "UPDATE tables SET status = 'available' WHERE table_id = ?";
            $update_from_stmt = $conn->prepare($update_from_sql);
            $update_from_stmt->bind_param("i", $order['table_id']);
            $update_from_stmt->execute();
            
            // Update to table status to occupied
            $update_to_sql = "UPDATE tables SET status = 'occupied' WHERE table_id = ?";
            $update_to_stmt = $conn->prepare($update_to_sql);
            $update_to_stmt->bind_param("i", $to_table_id);
            $update_to_stmt->execute();
            
            // Update order with new table ID
            $update_order_sql = "UPDATE orders SET table_id = ? WHERE order_id = ?";
            $update_order_stmt = $conn->prepare($update_order_sql);
            $update_order_stmt->bind_param("ii", $to_table_id, $order_id);
            $update_order_stmt->execute();
            
            // Record table transfer
            $user_id = $_SESSION['user_id'];
            $transfer_sql = "INSERT INTO table_transfers (order_id, from_table_id, to_table_id, transferred_by, reason) 
                            VALUES (?, ?, ?, ?, ?)";
            $transfer_stmt = $conn->prepare($transfer_sql);
            $transfer_stmt->bind_param("iiiis", $order_id, $order['table_id'], $to_table_id, $user_id, $reason);
            $transfer_stmt->execute();
            
            // Commit transaction
            $conn->commit();
            
            setFlashMessage('success', 'Chuyển bàn thành công');
            redirect(STAFF_URL . '/edit_order.php?id=' . $order_id);
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $error = 'Đã xảy ra lỗi khi chuyển bàn: ' . $e->getMessage();
        }
    }
}
?>

<div class="row mb-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= STAFF_URL ?>">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="<?= STAFF_URL ?>/tables.php">Quản lý bàn</a></li>
                <li class="breadcrumb-item"><a href="<?= STAFF_URL ?>/edit_order.php?id=<?= $order_id ?>">Đơn hàng #<?= $order_id ?></a></li>
                <li class="breadcrumb-item active" aria-current="page">Chuyển bàn</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Chuyển bàn cho đơn hàng #<?= $order_id ?></h5>
            </div>
            <div class="card-body">
                <?php if (isset($error) && !empty($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?= $error ?>
                        <button type="button" class="btn-close" data-mdb-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <form method="post" action="">
                    <div class="mb-4">
                        <label class="form-label">Bàn hiện tại</label>
                        <input type="text" class="form-control" value="<?= htmlspecialchars($order['table_number']) ?>" disabled>
                    </div>
                    
                    <div class="mb-4">
                        <label class="form-label">Chuyển đến bàn</label>
                        <select class="form-select" name="to_table_id" required>
                            <option value="">Chọn bàn</option>
                            <?php while ($table = $tables_result->fetch_assoc()): ?>
                                <?php if ($table['table_id'] != $order['table_id']): ?>
                                    <option value="<?= $table['table_id'] ?>" <?= ($table['status'] == 'occupied' || $table['status'] == 'maintenance') ? 'disabled' : '' ?>>
                                        <?= htmlspecialchars($table['table_number']) ?> 
                                        (<?= $table['capacity'] ?> người) - 
                                        <?php
                                        switch ($table['status']) {
                                            case 'available':
                                                echo '<span class="text-success">Trống</span>';
                                                break;
                                            case 'occupied':
                                                echo '<span class="text-danger">Đang sử dụng</span>';
                                                break;
                                            case 'reserved':
                                                echo '<span class="text-warning">Đã đặt trước</span>';
                                                break;
                                            case 'maintenance':
                                                echo '<span class="text-info">Bảo trì</span>';
                                                break;
                                        }
                                        ?>
                                    </option>
                                <?php endif; ?>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="form-label">Lý do chuyển bàn</label>
                        <textarea class="form-control" name="reason" rows="3"></textarea>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-exchange-alt me-2"></i>Chuyển bàn
                        </button>
                        <a href="<?= STAFF_URL ?>/edit_order.php?id=<?= $order_id ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Quay lại
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>